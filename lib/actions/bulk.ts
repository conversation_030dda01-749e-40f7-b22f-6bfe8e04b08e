"use server"

import { revalidatePath } from "next/cache"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError,
  ValidationError
} from "./shared"

export async function bulkAppraisalAction(
  action: string,
  employeeIds: string[]
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    // Validate session and permissions
    const session = await validateSession()
    await requirePermission('appraisal:write')
    if (!checkRateLimit(session.userId, 'bulk-action', 10, 60000)) {
      throw new RateLimitError()
    }

    if (!employeeIds || employeeIds.length === 0) {
      throw new ValidationError('No employees selected')
    }

    if (employeeIds.length > 50) {
      throw new ValidationError('Too many employees selected (max 50)')
    }

    let successCount = 0
    let errors: string[] = []

    switch (action) {
      case 'approve':
        for (const employeeId of employeeIds) {
          try {
            const { approveAppraisal } = await import('../data/index')
            await approveAppraisal(employeeId, session.userId)
            successCount++
          } catch (error) {
            errors.push(`Failed to approve ${employeeId}: ${error instanceof Error ? error.message : 'Unknown error'}`)
          }
        }
        break

      case 'ready-to-pay':
        for (const employeeId of employeeIds) {
          try {
            const { supabaseAdmin } = await import('../supabase')
            await supabaseAdmin.from('appy_appraisals')
              .update({ status: 'ready-to-pay' })
              .eq('employee_id', employeeId)
            successCount++
          } catch (error) {
            errors.push(`Failed to mark ${employeeId} ready to pay: ${error instanceof Error ? error.message : 'Unknown error'}`)
          }
        }
        break

      case 'contact-manager':
        for (const employeeId of employeeIds) {
          try {
            const { supabaseAdmin } = await import('../supabase')
            await supabaseAdmin.from('appy_appraisals')
              .update({ status: 'contact-manager' })
              .eq('employee_id', employeeId)
            successCount++
          } catch (error) {
            errors.push(`Failed to mark ${employeeId} contact manager: ${error instanceof Error ? error.message : 'Unknown error'}`)
          }
        }
        break

      case 'export':
        // This would generate a CSV/Excel export of selected employees
        // For now, just return success
        successCount = employeeIds.length
        break

      default:
        throw new ValidationError(`Unknown bulk action: ${action}`)
    }

    // Log the bulk action
    await logUserAction(`bulk:${action}`, {
      employeeIds,
      successCount,
      errorCount: errors.length,
      userId: session.userId
    })

    // Revalidate relevant pages
    revalidatePath("/dashboard")
    revalidatePath("/dashboard/approvals")

    if (errors.length > 0) {
      return {
        success: successCount > 0,
        message: `${successCount} items processed successfully, ${errors.length} errors occurred`,
        error: errors.join('; ')
      }
    }

    return {
      success: true,
      message: `Successfully processed ${successCount} items`
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}