import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from './auth'
import { canAccessAdminPage } from './admin-data'

export async function adminMiddleware(request: NextRequest) {
  const url = new URL(request.url)
  const pathname = url.pathname
  
  // Check if this is an admin route
  const adminRouteMatch = pathname.match(/^\/dashboard\/admin\/([^\/]+)$/)
  
  if (adminRouteMatch) {
    const adminId = adminRouteMatch[1]
    
    try {
      const currentUser = await getCurrentUser()
      
      if (!currentUser) {
        // Redirect to sign-in if not authenticated
        return NextResponse.redirect(new URL('/sign-in', request.url))
      }
      
      // Check if user has admin or super-admin role
      if (!['admin', 'super-admin'].includes(currentUser.role)) {
        // Redirect to dashboard with error if insufficient role
        return NextResponse.redirect(new URL('/dashboard?error=insufficient_permissions', request.url))
      }
      
      // Check if user can access this specific admin page
      const canAccess = await canAccessAdminPage(adminId, currentUser.id, currentUser.role)
      
      if (!canAccess) {
        // Redirect to dashboard with access denied error
        return NextResponse.redirect(new URL('/dashboard?error=access_denied', request.url))
      }
      
      // Allow access
      return NextResponse.next()
      
    } catch (error) {
      console.error('Admin middleware error:', error)
      // Redirect to sign-in on error
      return NextResponse.redirect(new URL('/sign-in', request.url))
    }
  }
  
  // Not an admin route, continue normally
  return NextResponse.next()
}

export function isAdminRoute(pathname: string): boolean {
  return /^\/dashboard\/admin\/[^\/]+$/.test(pathname)
}