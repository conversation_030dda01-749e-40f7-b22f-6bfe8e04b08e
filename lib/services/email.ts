import { Resend } from 'resend'
import { supabaseAdmin } from '../supabase'
import { debug } from '../debug'

// Initialize Resend with API key
const resend = new Resend(process.env.RESEND_API_KEY)

export interface EmailTemplate {
  subject: string
  html: string
  text: string
}

export interface PendingAppraisal {
  employee_id: string
  employee_name: string
  department_name: string
  days_remaining: number
}

export interface NotificationData {
  user_id: string
  manager_name: string
  manager_email: string
  period_end: string
  pending_appraisals: PendingAppraisal[]
}

/**
 * Email service for sending notifications
 */
export class EmailService {
  private static instance: EmailService
  private fromEmail: string

  private constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>'
  }

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService()
    }
    return EmailService.instance
  }

  /**
   * Send appraisal reminder email to manager
   */
  async sendAppraisalReminder(data: NotificationData): Promise<boolean> {
    try {
      debug.log('📧 [EMAIL] Sending appraisal reminder to:', data.manager_email)

      const template = this.generateAppraisalReminderTemplate(data)
      
      const result = await resend.emails.send({
        from: this.fromEmail,
        to: data.manager_email,
        subject: template.subject,
        html: template.html,
        text: template.text,
      })

      if (result.error) {
        console.error('❌ [EMAIL] Failed to send email:', result.error)
        await this.logEmailError(data.user_id, 'appraisal_reminder', template.subject, result.error.message)
        return false
      }

      debug.log('✅ [EMAIL] Email sent successfully:', result.data?.id)
      await this.logEmailSuccess(data.user_id, 'appraisal_reminder', template.subject, result.data?.id)
      return true

    } catch (error) {
      console.error('🚨 [EMAIL] Error sending appraisal reminder:', error)
      await this.logEmailError(data.user_id, 'appraisal_reminder', 'Appraisal Reminder', error instanceof Error ? error.message : 'Unknown error')
      return false
    }
  }

  /**
   * Generate HTML template for appraisal reminder
   */
  private generateAppraisalReminderTemplate(data: NotificationData): EmailTemplate {
    const subject = `⏰ Reminder: ${data.pending_appraisals.length} Pending Appraisal${data.pending_appraisals.length !== 1 ? 's' : ''} Due Soon`
    
    const employeeList = data.pending_appraisals
      .map(emp => `<li><strong>${emp.employee_name}</strong> (${emp.department_name})</li>`)
      .join('')

    const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Appraisal Reminder</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .content { background: white; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
    .employee-list { background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0; }
    .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 15px 0; }
    .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 14px; color: #6c757d; }
    .urgent { color: #dc3545; font-weight: bold; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔔 Appraisal Reminder</h1>
      <p>Hello ${data.manager_name},</p>
    </div>
    
    <div class="content">
      <p>This is a friendly reminder that you have <strong class="urgent">${data.pending_appraisals.length} pending employee appraisal${data.pending_appraisals.length !== 1 ? 's' : ''}</strong> that need to be completed by <strong>${new Date(data.period_end).toLocaleDateString()}</strong>.</p>
      
      <div class="employee-list">
        <h3>📋 Employees Awaiting Appraisal:</h3>
        <ul>
          ${employeeList}
        </ul>
      </div>
      
      <p>Please complete these appraisals as soon as possible to ensure timely processing and payment for your team members.</p>
      
      <a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/dashboard" class="button">
        Complete Appraisals Now →
      </a>
      
      <p><small>💡 <strong>Tip:</strong> You can save drafts and return later to complete them. Each appraisal takes approximately 5-10 minutes to complete.</small></p>
    </div>
    
    <div class="footer">
      <p>This is an automated reminder from the Employee Appraisal System.</p>
      <p>If you have any questions, please contact HR or your system administrator.</p>
    </div>
  </div>
</body>
</html>`

    const text = `
Appraisal Reminder

Hello ${data.manager_name},

This is a friendly reminder that you have ${data.pending_appraisals.length} pending employee appraisal${data.pending_appraisals.length !== 1 ? 's' : ''} that need to be completed by ${new Date(data.period_end).toLocaleDateString()}.

Employees Awaiting Appraisal:
${data.pending_appraisals.map(emp => `• ${emp.employee_name} (${emp.department_name})`).join('\n')}

Please complete these appraisals as soon as possible to ensure timely processing and payment for your team members.

Access the appraisal system: ${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/dashboard

This is an automated reminder from the Employee Appraisal System.
If you have any questions, please contact HR or your system administrator.
`

    return { subject, html, text }
  }

  /**
   * Log successful email send
   */
  private async logEmailSuccess(userId: string, type: string, subject: string, emailId?: string): Promise<void> {
    try {
      await supabaseAdmin.from('appy_notification_log').update({
        email_sent: true,
        email_sent_at: new Date().toISOString(),
        metadata: { email_id: emailId }
      }).eq('user_id', userId).eq('subject', subject).eq('email_sent', false)
    } catch (error) {
      console.error('Failed to log email success:', error)
    }
  }

  /**
   * Log email send error
   */
  private async logEmailError(userId: string, type: string, subject: string, error: string): Promise<void> {
    try {
      await supabaseAdmin.from('appy_notification_log').update({
        email_sent: false,
        email_error: error
      }).eq('user_id', userId).eq('subject', subject).eq('email_sent', false)
    } catch (logError) {
      console.error('Failed to log email error:', logError)
    }
  }

  /**
   * Test email configuration
   */
  async testEmailConfiguration(): Promise<boolean> {
    try {
      const testEmail = process.env.TEST_EMAIL || '<EMAIL>'
      
      const result = await resend.emails.send({
        from: this.fromEmail,
        to: testEmail,
        subject: 'Test Email - Appraisal System',
        html: '<p>This is a test email from the Appraisal System. Email configuration is working correctly!</p>',
        text: 'This is a test email from the Appraisal System. Email configuration is working correctly!'
      })

      return !result.error
    } catch (error) {
      console.error('Email configuration test failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const emailService = EmailService.getInstance()

// Console log for debugging
debug.log('📧 Email service initialized')
