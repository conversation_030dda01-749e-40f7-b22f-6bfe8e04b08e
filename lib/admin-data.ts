import { supabase } from './supabase'

export interface AdminProfile {
  id: string
  fullName: string
  email: string
  role: 'super-admin' | 'admin'
  supervisorId?: string
  supervisorName?: string
  permissions: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface AdminPermissions {
  canManageUsers: boolean
  canViewReports: boolean
  canExportData: boolean
  canManageSettings: boolean
  canViewAllAdmins: boolean
  canEditAdmins: boolean
}

// Admin profiles mapped by email for easier lookup
const adminProfilesMap: Record<string, Omit<AdminProfile, 'id'>> = {
  '<EMAIL>': {
    fullName: '<PERSON> Wazneh',
    email: '<EMAIL>',
    role: 'super-admin',
    supervisorId: undefined,
    supervisorName: undefined,
    permissions: {
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      canManageSettings: true,
      canViewAllAdmins: true,
      canEditAdmins: true
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  '<EMAIL>': {
    fullName: 'Tarek H',
    email: '<EMAIL>',
    role: 'admin',
    supervisorId: undefined, // Will be set dynamically
    supervisorName: 'Bob Wazneh',
    permissions: {
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      canManageSettings: false,
      canViewAllAdmins: false,
      canEditAdmins: false
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  '<EMAIL>': {
    fullName: 'Romy H',
    email: '<EMAIL>',
    role: 'admin',
    supervisorId: undefined, // Will be set dynamically
    supervisorName: 'Bob Wazneh',
    permissions: {
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      canManageSettings: false,
      canViewAllAdmins: false,
      canEditAdmins: false
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
}

// Function to get admin profile by user ID with database fallback
async function getAdminProfileFromDatabase(userId: string): Promise<AdminProfile | null> {
  try {
    const { data: user } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (!user) return null
    
    const { data: roleData } = await supabase
      .from('appy_user_roles')
      .select('role')
      .eq('user_id', userId)
      .single()
    
    const role = roleData?.role as 'super-admin' | 'admin' || 'admin'
    
    return {
      id: userId,
      fullName: user.full_name,
      email: user.email,
      role,
      supervisorId: undefined,
      supervisorName: undefined,
      permissions: getPermissionsForRole(role),
      createdAt: user.created_at,
      updatedAt: user.created_at
    }
  } catch (error) {
    console.error('Error fetching admin from database:', error)
    return null
  }
}

// Helper function to get permissions based on role
function getPermissionsForRole(role: 'super-admin' | 'admin'): AdminPermissions {
  if (role === 'super-admin') {
    return {
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      canManageSettings: true,
      canViewAllAdmins: true,
      canEditAdmins: true
    }
  }
  
  return {
    canManageUsers: true,
    canViewReports: true,
    canExportData: true,
    canManageSettings: false,
    canViewAllAdmins: false,
    canEditAdmins: false
  }
}

export async function getAdminProfile(adminId: string): Promise<AdminProfile | null> {
  // First try to get from database
  const dbProfile = await getAdminProfileFromDatabase(adminId)
  if (dbProfile) {
    return dbProfile
  }
  
  // Fallback to mock data (for development)
  // This won't work in production since we don't have the actual user IDs
  console.warn('Using mock admin data - this should be replaced with database queries')
  return null
}

export async function getAdminPermissions(adminId: string): Promise<AdminPermissions> {
  const admin = await getAdminProfile(adminId)
  
  if (!admin) {
    return {
      canManageUsers: false,
      canViewReports: false,
      canExportData: false,
      canManageSettings: false,
      canViewAllAdmins: false,
      canEditAdmins: false
    }
  }
  
  return admin.permissions as AdminPermissions
}

export async function getAllAdmins(currentUserId?: string, currentUserRole?: string): Promise<AdminProfile[]> {
  if (!currentUserId || !currentUserRole) return []
  
  try {
    // Super-admin can see all admins
    if (currentUserRole === 'super-admin') {
      const { data: managers } = await supabase
        .from('appy_managers')
        .select(`
          user_id,
          full_name,
          email,
          created_at,
          appy_user_roles (role)
        `)
        .eq('active', true)
      
      if (managers) {
        return managers.map(manager => ({
          id: manager.user_id,
          fullName: manager.full_name,
          email: manager.email,
          role: (manager.appy_user_roles as any)?.role || 'admin',
          supervisorId: undefined,
          supervisorName: undefined,
          permissions: getPermissionsForRole((manager.appy_user_roles as any)?.role || 'admin'),
          createdAt: manager.created_at,
          updatedAt: manager.created_at
        }))
      }
    }
    
    // Regular admins can only see themselves
    const profile = await getAdminProfile(currentUserId)
    return profile ? [profile] : []
  } catch (error) {
    console.error('Error fetching admins:', error)
    return []
  }
}

export async function canAccessAdminPage(adminId: string, currentUserId?: string, currentUserRole?: string): Promise<boolean> {
  if (!currentUserId || !currentUserRole) return false
  
  // Super-admin can access all admin pages
  if (currentUserRole === 'super-admin') {
    return true
  }
  
  // Regular admins can only access their own page
  return currentUserId === adminId
}

export async function getAdminStats(adminId: string, currentUserId?: string, currentUserRole?: string) {
  if (!currentUserId || !currentUserRole || !await canAccessAdminPage(adminId, currentUserId, currentUserRole)) {
    return null
  }
  
  // Mock stats - in production, query actual database
  return {
    totalEmployees: 15,
    activeAppraisals: 8,
    completedAppraisals: 12,
    pendingAppraisals: 3,
    departmentCount: 4,
    managerCount: 5,
    monthlyGrowth: 5.2,
    approvalRate: 85.7
  }
}

export async function updateAdminProfile(adminId: string, updates: Partial<AdminProfile>, currentUserRole?: string): Promise<AdminProfile | null> {
  if (!currentUserRole || currentUserRole !== 'super-admin') {
    throw new Error('Insufficient permissions')
  }
  
  try {
    // Update manager profile
    if (updates.fullName || updates.email) {
      const { error } = await supabase
        .from('appy_managers')
        .update({
          full_name: updates.fullName,
          email: updates.email
        })
        .eq('user_id', adminId)
      
      if (error) throw error
    }
    
    // Update role if provided
    if (updates.role) {
      const { error } = await supabase
        .from('appy_user_roles')
        .upsert({
          user_id: adminId,
          role: updates.role
        })
      
      if (error) throw error
    }
    
    // Return updated profile
    return await getAdminProfile(adminId)
  } catch (error) {
    console.error('Error updating admin profile:', error)
    throw new Error('Failed to update admin profile')
  }
}

export async function getAdminByEmail(email: string): Promise<AdminProfile | null> {
  // First try to get from database
  try {
    const { data: user } = await supabase
      .from('appy_managers')
      .select('*')
      .eq('email', email)
      .single()
    
    if (user) {
      const { data: roleData } = await supabase
        .from('appy_user_roles')
        .select('role')
        .eq('user_id', user.user_id)
        .single()
      
      const role = roleData?.role as 'super-admin' | 'admin' || 'admin'
      
      return {
        id: user.user_id,
        fullName: user.full_name,
        email: user.email,
        role,
        supervisorId: undefined,
        supervisorName: undefined,
        permissions: getPermissionsForRole(role),
        createdAt: user.created_at,
        updatedAt: user.created_at
      }
    }
  } catch (error) {
    console.error('Error fetching admin by email:', error)
  }
  
  // Fallback to mock data lookup
  const mockProfile = adminProfilesMap[email]
  if (mockProfile) {
    return {
      id: `mock_${email}`, // Temporary ID for mock data
      ...mockProfile
    }
  }
  
  return null
}