import type { MockUser, UserR<PERSON> } from '../types'

// Fallback mock users for development and testing
export const fallbackMockUsers: Record<UserRole, MockUser> = {
  manager: { id: "a1b2c3d4-e5f6-7890-1234-567890abcdef", fullName: "<PERSON>", role: "manager" },
  accountant: { id: "b2c3d4e5-f6g7-8901-2345-678901bcdefg", fullName: "<PERSON>", role: "accountant" },
  admin: { id: "tarek_clerk_user_id_placeholder", fullName: "Tarek H", role: "admin" },
  "hr-admin": { id: "c3d4e5f6-g7h8-9012-3456-789012cdefgh", fullName: "<PERSON><PERSON>", role: "hr-admin" },
  "super-admin": { id: "bob_clerk_user_id_placeholder", fullName: "<PERSON> Wazneh", role: "super-admin" },
  employee: { id: "d4e5f6g7-h8i9-0123-4567-890123defghi", fullName: "<PERSON>loyee", role: "employee" },
}

export async function getLoggedInUser(role: UserRole = "manager"): Promise<MockUser> {
  // This function is kept for backward compatibility during migration
  // In production, user data should come from Clerk
  return fallbackMockUsers[role]
}