import { supabaseAdmin, supabaseAdminQuery } from '../supabase'
import type { 
  EmployeeFeedback, 
  FeedbackFormData, 
  FeedbackComment, 
  FeedbackStatusHistory, 
  FeedbackStatistics,
  FeedbackStatus 
} from '../types'
import { debug } from '../debug'

/**
 * Submit new employee feedback
 */
export async function submitFeedback(
  submitterId: string, 
  feedbackData: FeedbackFormData
): Promise<{ success: boolean; feedbackId?: string; error?: string }> {
  try {
    debug.log('📝 [FEEDBACK] Submitting new feedback:', {
      submitterId,
      type: feedbackData.feedbackType,
      isAnonymous: feedbackData.isAnonymous
    })

    const { data, error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .insert({
        submitter_id: submitterId,
        target_employee_id: feedbackData.targetEmployeeId || null,
        feedback_type: feedbackData.feedbackType,
        category: feedbackData.category || null,
        subject: feedbackData.subject,
        message: feedbackData.message,
        is_anonymous: feedbackData.isAnonymous,
        priority: feedbackData.priority,
        status: 'pending'
      })
      .select('id')
      .single()

    if (error) {
      console.error('❌ [FEEDBACK] Failed to submit feedback:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [FEEDBACK] Feedback submitted successfully:', data.id)
    return { success: true, feedbackId: data.id }

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error submitting feedback:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Get feedback submitted by a specific employee
 */
export async function getEmployeeFeedback(employeeId: string): Promise<EmployeeFeedback[]> {
  try {
    debug.log('📋 [FEEDBACK] Fetching feedback for employee:', employeeId)

    const { data, error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .select(`
        *,
        target_employee:target_employee_id(full_name),
        reviewed_by_manager:reviewed_by(full_name),
        resolved_by_manager:resolved_by(full_name)
      `)
      .eq('submitter_id', employeeId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch employee feedback:', error)
      return []
    }

    return (data || []).map(mapFeedbackFromDB)

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching employee feedback:', error)
    return []
  }
}

/**
 * Get all pending feedback for HR review
 */
export async function getPendingFeedbackForHR(): Promise<EmployeeFeedback[]> {
  try {
    debug.log('🔍 [FEEDBACK] Fetching pending feedback for HR')

    const { data, error } = await supabaseAdmin
      .rpc('get_pending_feedback_for_hr')

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch pending feedback:', error)
      return []
    }

    // Convert RPC result to EmployeeFeedback format
    return (data || []).map((item: any) => ({
      id: item.feedback_id,
      submitterId: '', // Not needed for HR view
      submitterName: item.submitter_name,
      targetEmployeeId: null,
      targetEmployeeName: item.target_name,
      feedbackType: item.feedback_type,
      subject: item.subject,
      priority: item.priority,
      status: 'pending' as FeedbackStatus,
      createdAt: item.created_at,
      daysPending: item.days_pending
    }))

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching pending feedback:', error)
    return []
  }
}

/**
 * Get detailed feedback by ID (for HR review)
 */
export async function getFeedbackById(feedbackId: string): Promise<EmployeeFeedback | null> {
  try {
    debug.log('🔍 [FEEDBACK] Fetching feedback details:', feedbackId)

    const { data, error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .select(`
        *,
        submitter:submitter_id(full_name, email),
        target_employee:target_employee_id(full_name, email),
        reviewed_by_manager:reviewed_by(full_name),
        resolved_by_manager:resolved_by(full_name)
      `)
      .eq('id', feedbackId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('❌ [FEEDBACK] Failed to fetch feedback details:', error)
      return null
    }

    return mapFeedbackFromDB(data)

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching feedback details:', error)
    return null
  }
}

/**
 * Update feedback status (HR action)
 */
export async function updateFeedbackStatus(
  feedbackId: string,
  status: FeedbackStatus,
  reviewedBy: string,
  hrResponse?: string,
  hrNotes?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    debug.log('📝 [FEEDBACK] Updating feedback status:', {
      feedbackId,
      status,
      reviewedBy
    })

    const updateData: any = {
      status,
      reviewed_by: reviewedBy,
      reviewed_at: new Date().toISOString()
    }

    if (hrResponse) {
      updateData.hr_response = hrResponse
    }

    if (hrNotes) {
      updateData.hr_notes = hrNotes
    }

    if (status === 'resolved') {
      updateData.resolved_by = reviewedBy
      updateData.resolved_at = new Date().toISOString()
    }

    const { error } = await supabaseAdmin
      .from('appy_employee_feedback')
      .update(updateData)
      .eq('id', feedbackId)

    if (error) {
      console.error('❌ [FEEDBACK] Failed to update feedback status:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [FEEDBACK] Feedback status updated successfully')
    return { success: true }

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error updating feedback status:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Add comment to feedback (HR internal discussion)
 */
export async function addFeedbackComment(
  feedbackId: string,
  commenterId: string,
  comment: string,
  isInternal: boolean = true
): Promise<{ success: boolean; error?: string }> {
  try {
    debug.log('💬 [FEEDBACK] Adding comment to feedback:', feedbackId)

    const { error } = await supabaseAdmin
      .from('appy_feedback_comments')
      .insert({
        feedback_id: feedbackId,
        commenter_id: commenterId,
        comment,
        is_internal: isInternal
      })

    if (error) {
      console.error('❌ [FEEDBACK] Failed to add comment:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [FEEDBACK] Comment added successfully')
    return { success: true }

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error adding comment:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }
  }
}

/**
 * Get feedback comments
 */
export async function getFeedbackComments(feedbackId: string): Promise<FeedbackComment[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('appy_feedback_comments')
      .select(`
        *,
        commenter:commenter_id(full_name)
      `)
      .eq('feedback_id', feedbackId)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch comments:', error)
      return []
    }

    return (data || []).map((comment: any) => ({
      id: comment.id,
      feedbackId: comment.feedback_id,
      commenterId: comment.commenter_id,
      commenterName: comment.commenter?.full_name || 'Unknown',
      comment: comment.comment,
      isInternal: comment.is_internal,
      createdAt: comment.created_at,
      updatedAt: comment.updated_at
    }))

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching comments:', error)
    return []
  }
}

/**
 * Get feedback statistics for HR dashboard
 */
export async function getFeedbackStatistics(
  startDate?: string,
  endDate?: string
): Promise<FeedbackStatistics | null> {
  try {
    debug.log('📊 [FEEDBACK] Fetching feedback statistics')

    const { data, error } = await supabaseAdmin
      .rpc('get_feedback_statistics', {
        start_date: startDate || null,
        end_date: endDate || null
      })
      .single()

    if (error) {
      console.error('❌ [FEEDBACK] Failed to fetch statistics:', error)
      return null
    }

    return {
      totalFeedback: data.total_feedback || 0,
      pendingCount: data.pending_count || 0,
      underReviewCount: data.under_review_count || 0,
      resolvedCount: data.resolved_count || 0,
      byType: data.by_type || {},
      byPriority: data.by_priority || {},
      avgResolutionDays: data.avg_resolution_days || 0
    }

  } catch (error) {
    console.error('🚨 [FEEDBACK] Error fetching statistics:', error)
    return null
  }
}

/**
 * Helper function to map database feedback to TypeScript type
 */
function mapFeedbackFromDB(data: any): EmployeeFeedback {
  return {
    id: data.id,
    submitterId: data.submitter_id,
    submitterName: data.is_anonymous ? 'Anonymous' : data.submitter?.full_name,
    targetEmployeeId: data.target_employee_id,
    targetEmployeeName: data.target_employee?.full_name || null,
    feedbackType: data.feedback_type,
    category: data.category,
    subject: data.subject,
    message: data.message,
    isAnonymous: data.is_anonymous,
    priority: data.priority,
    status: data.status,
    hrResponse: data.hr_response,
    hrNotes: data.hr_notes,
    reviewedBy: data.reviewed_by,
    reviewedAt: data.reviewed_at,
    resolutionSummary: data.resolution_summary,
    resolvedBy: data.resolved_by,
    resolvedAt: data.resolved_at,
    requiresFollowup: data.requires_followup,
    followupDate: data.followup_date,
    followupNotes: data.followup_notes,
    createdAt: data.created_at,
    updatedAt: data.updated_at
  }
}

debug.log('📝 Feedback data service initialized')
