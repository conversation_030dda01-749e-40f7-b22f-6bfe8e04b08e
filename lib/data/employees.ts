import type { Employee, EmployeeDetails, EmployeeKPI } from '../types'
import { db } from '../db'
import { supabase, supabaseAdminQuery } from '../supabase'

export async function getEmployees(): Promise<Employee[]> {
  try {
    const employees = await db.getEmployees()
    return employees.map(emp => ({
      id: emp.id,
      fullName: emp.full_name,
      compensation: emp.rate as 'hourly' | 'monthly', // DB rate field contains the compensation type
      rate: Number(emp.compensation), // DB compensation field contains the numeric rate
      departmentId: emp.department_id || '',
      departmentName: emp.department_name,
      managerId: emp.manager_id,
      managerName: emp.manager_name,
      active: emp.active
    }))
  } catch (error) {
    console.error('Failed to fetch employees from database:', error)
    // Fallback to empty array in case of database error
    return []
  }
}

export async function saveEmployee(emp: Partial<Employee>): Promise<void> {
  try {
    if (emp.id) {
      await db.updateEmployee(emp.id, {
        fullName: emp.fullName,
        email: emp.email,
        compensation: emp.rate, // DB expects numeric rate in compensation field
        rate: emp.compensation, // DB expects compensation type in rate field
        departmentId: emp.departmentId,
        managerId: emp.managerId || undefined,
        active: emp.active
      })
    } else {
      await db.createEmployee({
        fullName: emp.fullName!,
        email: emp.email!,
        compensation: emp.rate!, // DB expects numeric rate in compensation field
        rate: emp.compensation!, // DB expects compensation type in rate field
        departmentId: emp.departmentId!,
        managerId: emp.managerId || undefined
      })
    }
  } catch (error) {
    console.error('Failed to save employee to database:', error)
    throw new Error('Failed to save employee')
  }
}

export async function getEmployeeDetails(employeeId: string): Promise<EmployeeDetails | null> {
  try {
    const employee = await db.getEmployeeById(employeeId)
    if (!employee) return null

    return {
      id: employee.id,
      fullName: employee.full_name,
      departmentName: employee.department_name || 'N/A',
      compensation: employee.rate as 'hourly' | 'monthly', // DB rate field contains compensation type
      rate: Number(employee.compensation), // DB compensation field contains numeric rate
    }
  } catch (error) {
    console.error('Failed to fetch employee details:', error)
    return null
  }
}

/**
 * Get all employees under a manager's hierarchy (including sub-managers and their employees)
 * @param managerId The manager's user ID
 * @returns Array of employees in the hierarchy with their hierarchy level
 */
export async function getEmployeesForManager(managerId: string): Promise<Employee[]> {
  try {
    console.log('🔍 [DEBUG] getEmployeesForManager - Fetching hierarchical employees for manager:', managerId)

    const { data, error } = await supabase.rpc('get_hierarchical_employees', {
      manager_user_id: managerId
    })

    if (error) {
      console.error('🚨 [ERROR] getEmployeesForManager - Supabase RPC error:', error)
      throw error
    }

    if (!data) {
      console.log('📝 [DEBUG] getEmployeesForManager - No data returned for manager:', managerId)
      return []
    }

    console.log('✅ [DEBUG] getEmployeesForManager - Found', data.length, 'employees in hierarchy')
    console.log('📊 [DEBUG] getEmployeesForManager - Hierarchy breakdown:',
      data.reduce((acc: any, emp: any) => {
        acc[`Level ${emp.hierarchy_level}`] = (acc[`Level ${emp.hierarchy_level}`] || 0) + 1
        return acc
      }, {})
    )

    // Transform the data to match our Employee type
    const employees: Employee[] = data.map((emp: any) => ({
      id: emp.id,
      fullName: emp.full_name,
      compensation: emp.rate as 'hourly' | 'monthly', // DB rate field contains the compensation type
      rate: Number(emp.compensation), // DB compensation field contains the numeric rate
      departmentId: emp.department_id || '',
      departmentName: emp.department_name || '',
      managerId: emp.manager_id,
      managerName: emp.manager_name || '',
      active: emp.active
    }))

    console.log('🎯 [DEBUG] getEmployeesForManager - Returning', employees.length, 'formatted employees')
    return employees

  } catch (error) {
    console.error('🚨 [ERROR] getEmployeesForManager - Failed to fetch hierarchical employees:', error)
    // Fallback to empty array in case of error
    return []
  }
}

/**
 * Get employee profile with all details
 * @param employeeId The employee ID
 * @returns Employee with profile fields or null
 */
export async function getEmployeeProfile(employeeId: string): Promise<Employee | null> {
  try {
    const { data, error } = await supabaseAdminQuery
      .employees()
      .select('*, appy_departments(name)')
      .eq('id', employeeId)
      .single()

    if (error) {
      console.error('Error fetching employee profile:', error)
      return null
    }

    if (!data) return null

    return {
      id: data.id,
      fullName: data.full_name,
      firstName: data.first_name || undefined,
      lastName: data.last_name || undefined,
      email: data.email || undefined,
      bio: data.bio || undefined,
      linkedinUrl: data.linkedin_url || undefined,
      twitterUrl: data.twitter_url || undefined,
      compensation: data.rate,
      rate: Number(data.compensation),
      departmentId: data.department_id || '',
      departmentName: data.appy_departments?.name,
      managerId: data.manager_id,
      active: data.active,
      updatedAt: data.updated_at || undefined
    }
  } catch (error) {
    console.error('Failed to fetch employee profile:', error)
    return null
  }
}

/**
 * Get employee KPIs
 * @param employeeId The employee ID
 * @returns Array of KPIs
 */
export async function getEmployeeKPIs(employeeId: string): Promise<EmployeeKPI[]> {
  try {
    const { data, error } = await supabaseAdminQuery
      .employeeKpis()
      .select('*')
      .eq('employee_id', employeeId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching employee KPIs:', error)
      return []
    }

    return data.map(kpi => ({
      id: kpi.id,
      employeeId: kpi.employee_id,
      kpiName: kpi.kpi_name,
      kpiValue: kpi.kpi_value || undefined,
      kpiTarget: kpi.kpi_target || undefined,
      kpiUnit: kpi.kpi_unit || undefined,
      period: kpi.period || undefined,
      description: kpi.description || undefined,
      createdAt: kpi.created_at,
      updatedAt: kpi.updated_at
    }))
  } catch (error) {
    console.error('Failed to fetch employee KPIs:', error)
    return []
  }
}