import type {
  AccountingViewData,
  AccountingStats,
  Employee,
  SalaryAdjustment,
  AccountingComment,
  PaymentProcessingLog,
  AccountingSummary,
  AppraisalForAccountingReview,
  SalaryAdjustmentType,
  CommentType,
  CommentPriority
} from '../types'
import { db } from '../db'
import { supabaseAdmin } from '../supabase'
import { getEmployees, getEmployeesForManager } from './employees'
import { getPeriods } from './periods'
import { debug } from '../debug'

export async function getAccountingData(): Promise<AccountingViewData[]> {
  try {
    const employees = await getEmployees()
    // TODO: In real implementation, this would join with appraisals table to get actual status and submission times
    return employees.map((emp) => {
      const hours = emp.compensation === "hourly" ? 160 : 0 // Standard work hours for hourly
      const totalAmount = emp.compensation === "hourly"
        ? emp.rate * hours
        : emp.rate // Monthly rate is the total amount

      return {
        employeeId: emp.id,
        employeeName: emp.fullName,
        departmentName: emp.departmentName!,
        managerName: emp.managerName || 'Unassigned',
        status: "not-started" as const, // In real app, this would come from appraisals table
        submittedAt: null, // In real app, this would come from appraisals table
        compensation: emp.compensation,
        rate: emp.rate,
        hours,
        paymentStatus: null, // No payment status without appraisal
        totalAmount,
        appraisalId: undefined,
      }
    })
  } catch (error) {
    console.error('Failed to fetch accounting data:', error)
    return []
  }
}

export async function getAccountingDataForUser(): Promise<AccountingViewData[]> {
  try {
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }
    
    // Get current active period
    const periods = await getPeriods()
    const currentPeriod = periods.find(p => !p.closed)
    
    if (!currentPeriod) {
      console.warn('No active appraisal period found')
      return []
    }
    
    // Filter based on user role
    let filteredEmployees: Employee[]

    if (currentUser.role === 'super-admin') {
      // Super admins see all employees
      const employees = await getEmployees()
      filteredEmployees = employees
      console.log('🔍 [DEBUG] Accounting - Super admin sees all employees:', employees.length)
    } else if (currentUser.role === 'accountant') {
      // Accountants see all employees (for payroll processing)
      const employees = await getEmployees()
      filteredEmployees = employees
      console.log('🔍 [DEBUG] Accounting - Accountant sees all employees:', employees.length)
    } else if (currentUser.role === 'manager') {
      // Managers see employees under their hierarchical supervision (including sub-managers)
      filteredEmployees = await getEmployeesForManager(currentUser.id)
      console.log('🌳 [DEBUG] Accounting - Manager sees hierarchical employees:', filteredEmployees.length)
    } else if (currentUser.role === 'hr-admin' || currentUser.role === 'admin') {
      // HR admins and admins see all employees
      const employees = await getEmployees()
      filteredEmployees = employees
      console.log('🔍 [DEBUG] Accounting - HR/Admin sees all employees:', employees.length)
    } else {
      // Default: no access
      filteredEmployees = []
      console.log('🚫 [DEBUG] Accounting - No access for role:', currentUser.role)
    }
    
    // Get appraisals for the current period
    const appraisals = await db.getAppraisalsWithEmployeeData(currentPeriod.id)
    
    // Create a map of employee ID to appraisal data
    const appraisalMap = new Map(appraisals.map(appraisal => [appraisal.employee_id, appraisal]))
    
    // Build the accounting view data with real appraisal statuses
    return filteredEmployees.map((emp) => {
      const appraisal = appraisalMap.get(emp.id)

      // Map database status to UI status
      let status: 'not-started' | 'draft' | 'submitted' | 'ready-to-pay' | 'contact-manager' = 'not-started'
      let submittedAt: string | null = null
      let paymentStatus: 'ready-to-pay' | 'contact-manager' | null = null
      let appraisalId: string | undefined = undefined

      if (appraisal) {
        appraisalId = appraisal.id
        paymentStatus = appraisal.payment_status

        // If payment status is set, use that as the main status
        if (appraisal.payment_status) {
          status = appraisal.payment_status
          submittedAt = appraisal.submitted_at
        } else if (appraisal.status === 'submitted') {
          status = 'submitted'
          submittedAt = appraisal.submitted_at
        } else if (appraisal.status === 'pending') {
          status = 'draft'
        }
      }

      // Calculate total payment amount
      const hours = emp.compensation === "hourly" ? 160 : 0 // Standard work hours for hourly
      const totalAmount = emp.compensation === "hourly"
        ? emp.rate * hours
        : emp.rate // Monthly rate is the total amount

      console.log(`💰 [DEBUG] Accounting - Employee ${emp.fullName}: ${emp.compensation} at $${emp.rate}, total: $${totalAmount}`)

      return {
        employeeId: emp.id,
        employeeName: emp.fullName,
        departmentName: emp.departmentName!,
        managerName: emp.managerName || 'Unassigned',
        status,
        submittedAt,
        compensation: emp.compensation,
        rate: emp.rate,
        hours,
        paymentStatus,
        totalAmount,
        appraisalId,
      }
    })
  } catch (error) {
    console.error('Failed to fetch accounting data for user:', error)
    return []
  }
}

export async function getAccountingStats(): Promise<AccountingStats> {
  try {
    console.log('📊 [DEBUG] Calculating accounting statistics...')

    const accountingData = await getAccountingDataForUser()

    const stats = {
      totalEmployees: accountingData.length,
      readyToPay: accountingData.filter(emp => emp.paymentStatus === 'ready-to-pay').length,
      contactManager: accountingData.filter(emp => emp.paymentStatus === 'contact-manager').length,
      totalPaymentAmount: accountingData
        .filter(emp => emp.paymentStatus === 'ready-to-pay')
        .reduce((sum, emp) => sum + emp.totalAmount, 0),
      hourlyEmployees: accountingData.filter(emp => emp.compensation === 'hourly').length,
      monthlyEmployees: accountingData.filter(emp => emp.compensation === 'monthly').length,
      submittedAppraisals: accountingData.filter(emp => emp.status === 'submitted' || emp.paymentStatus).length,
      pendingAppraisals: accountingData.filter(emp => emp.status === 'draft' || emp.status === 'not-started').length,
    }

    console.log('📊 [DEBUG] Accounting stats calculated:', stats)
    return stats
  } catch (error) {
    console.error('Failed to calculate accounting stats:', error)
    return {
      totalEmployees: 0,
      readyToPay: 0,
      contactManager: 0,
      totalPaymentAmount: 0,
      hourlyEmployees: 0,
      monthlyEmployees: 0,
      submittedAppraisals: 0,
      pendingAppraisals: 0,
    }
  }
}

// ===== NEW SALARY DEDUCTION AND ACCOUNTING FUNCTIONS =====

/**
 * Add accounting notes and salary adjustments to an appraisal
 */
export async function addAccountingNotes(
  appraisalId: string,
  notes: string,
  reviewedBy: string,
  adjustmentAmount?: number,
  adjustmentType?: SalaryAdjustmentType,
  adjustmentReason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    debug.log('💰 [ACCOUNTING] Adding accounting notes:', {
      appraisalId,
      adjustmentAmount,
      adjustmentType
    })

    const { data, error } = await supabaseAdmin
      .rpc('add_accounting_notes', {
        p_appraisal_id: appraisalId,
        p_notes: notes,
        p_adjustment_amount: adjustmentAmount || null,
        p_adjustment_type: adjustmentType || null,
        p_adjustment_reason: adjustmentReason || null,
        p_reviewed_by: reviewedBy
      })

    if (error) {
      console.error('❌ [ACCOUNTING] Failed to add accounting notes:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [ACCOUNTING] Accounting notes added successfully')
    return { success: true }

  } catch (error) {
    console.error('🚨 [ACCOUNTING] Error adding accounting notes:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Get enhanced accounting summary statistics
 */
export async function getEnhancedAccountingSummary(periodId?: string): Promise<AccountingSummary | null> {
  try {
    debug.log('📊 [ACCOUNTING] Fetching enhanced accounting summary')

    const { data, error } = await supabaseAdmin
      .rpc('get_accounting_summary', {
        p_period_id: periodId || null
      })
      .single()

    if (error) {
      console.error('❌ [ACCOUNTING] Failed to fetch summary:', error)
      return null
    }

    return {
      totalAppraisals: (data as any)?.total_appraisals || 0,
      pendingReview: (data as any)?.pending_review || 0,
      reviewedCount: (data as any)?.reviewed_count || 0,
      processedCount: (data as any)?.processed_count || 0,
      totalAdjustments: (data as any)?.total_adjustments || 0,
      totalDeductions: (data as any)?.total_deductions || 0,
      totalBonuses: (data as any)?.total_bonuses || 0
    }

  } catch (error) {
    console.error('🚨 [ACCOUNTING] Error fetching summary:', error)
    return null
  }
}

/**
 * Get appraisals needing accounting review
 */
export async function getAppraisalsForAccountingReview(): Promise<AppraisalForAccountingReview[]> {
  try {
    debug.log('📋 [ACCOUNTING] Fetching appraisals for review')

    const { data, error } = await supabaseAdmin
      .rpc('get_appraisals_for_accounting_review')

    if (error) {
      console.error('❌ [ACCOUNTING] Failed to fetch appraisals for review:', error)
      return []
    }

    return (data || []).map((item: any) => ({
      appraisalId: item.appraisal_id,
      employeeName: item.employee_name,
      departmentName: item.department_name,
      managerName: item.manager_name,
      submittedDate: item.submitted_date,
      paymentStatus: item.payment_status,
      currentAdjustmentAmount: item.current_adjustment_amount,
      currentAdjustmentType: item.current_adjustment_type,
      daysPending: item.days_pending
    }))

  } catch (error) {
    console.error('🚨 [ACCOUNTING] Error fetching appraisals for review:', error)
    return []
  }
}

/**
 * Add accounting comment to an appraisal
 */
export async function addAccountingComment(
  appraisalId: string,
  commenterId: string,
  comment: string,
  commentType: CommentType = 'general',
  priority: CommentPriority = 'normal',
  isInternal: boolean = true
): Promise<{ success: boolean; error?: string }> {
  try {
    debug.log('💬 [ACCOUNTING] Adding accounting comment:', appraisalId)

    const { error } = await supabaseAdmin
      .from('appy_accounting_comments')
      .insert({
        appraisal_id: appraisalId,
        commenter_id: commenterId,
        comment,
        comment_type: commentType,
        priority,
        is_internal: isInternal
      })

    if (error) {
      console.error('❌ [ACCOUNTING] Failed to add comment:', error)
      return { success: false, error: error.message }
    }

    debug.log('✅ [ACCOUNTING] Comment added successfully')
    return { success: true }

  } catch (error) {
    console.error('🚨 [ACCOUNTING] Error adding comment:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}