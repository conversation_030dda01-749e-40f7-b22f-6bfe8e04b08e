"use client"

import { useState } from "react"
import { toast } from "sonner"
import { formatDistanceToNow } from "date-fns"
import {
  Check,
  X,
  Clock,
  User,
  MoreHorizontal,
  Eye,
  XCircle,
  CheckCircle,
  ClockIcon,
} from "lucide-react"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"

import { approveAppraisalAction, rejectAppraisalAction } from "@/lib/actions"
import type { EmployeeAppraisal, AppraisalStatus } from "@/lib/types"
import { useRouter } from "next/navigation"

interface AppraisalApprovalsTableProps {
  appraisals: EmployeeAppraisal[]
  onApprovalUpdate?: () => void
  title?: string
  description?: string
  emptyMessage?: string
}

const statusConfig = {
  submitted: {
    label: 'Pending Approval',
    icon: Clock,
    className: 'bg-blue-100 text-blue-800',
  },
  approved: {
    label: 'Approved',
    icon: CheckCircle,
    className: 'bg-green-100 text-green-800',
  },
  rejected: {
    label: 'Rejected',
    icon: XCircle,
    className: 'bg-red-100 text-red-800',
  },
  'ready-to-pay': {
    label: 'Ready to Pay',
    icon: CheckCircle,
    className: 'bg-emerald-100 text-emerald-800',
  },
  'contact-manager': {
    label: 'Contact Manager',
    icon: Clock,
    className: 'bg-orange-100 text-orange-800',
  },
}

export function AppraisalApprovalsTable({
  appraisals,
  onApprovalUpdate,
  title = 'Appraisal Approvals',
  description,
  emptyMessage = 'No appraisals pending approval.',
}: AppraisalApprovalsTableProps) {
  const [selectedAppraisal, setSelectedAppraisal] = useState<EmployeeAppraisal | null>(null)
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false)
  const [rejectionReason, setRejectionReason] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const router = useRouter()

  const handleApproval = async (appraisalId: string, action: 'approve' | 'reject') => {
    setIsSubmitting(true)
    
    try {
      const result = action === 'approve'
        ? await approveAppraisalAction(appraisalId)
        : await rejectAppraisalAction(appraisalId, rejectionReason)

      if (result.success) {
        toast.success('message' in result ? result.message : `Appraisal ${action}d successfully`)
        setIsApprovalDialogOpen(false)
        setRejectionReason('')
        onApprovalUpdate?.()
      } else {
        toast.error('error' in result ? result.error : `Failed to ${action} appraisal`)
      }
    } catch (error) {
      toast.error('An unexpected error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const StatusBadge = ({ status }: { status: AppraisalStatus }) => {
    const config = statusConfig[status as keyof typeof statusConfig]
    if (!config) return null
    
    const Icon = config.icon
    
    return (
      <Badge className={config.className}>
        <Icon className="mr-1 h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {appraisals.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p className="text-gray-500 text-sm">{emptyMessage}</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Department</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {appraisals.map((appraisal) => (
                <TableRow key={appraisal.employeeId}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">{appraisal.fullName}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{appraisal.departmentName}</div>
                  </TableCell>
                  <TableCell>
                    <StatusBadge status={appraisal.status} />
                  </TableCell>
                  <TableCell>
                    {appraisal.submittedAt && (
                      <div className="text-sm text-gray-500">
                        {formatDistanceToNow(new Date(appraisal.submittedAt), { addSuffix: true })}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => router.push(`/dashboard/appraisal/${appraisal.employeeId}`)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        {appraisal.status === 'submitted' && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedAppraisal(appraisal)
                                setIsApprovalDialogOpen(true)
                              }}
                            >
                              <Check className="mr-2 h-4 w-4" />
                              Approve/Reject
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Approval Dialog */}
      <Dialog open={isApprovalDialogOpen} onOpenChange={setIsApprovalDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Approve Appraisal</DialogTitle>
            <DialogDescription>
              {selectedAppraisal && `Review ${selectedAppraisal.fullName}'s appraisal submission`}
            </DialogDescription>
          </DialogHeader>
          {selectedAppraisal && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Employee:</strong> {selectedAppraisal.fullName}
                  </div>
                  <div>
                    <strong>Department:</strong> {selectedAppraisal.departmentName}
                  </div>
                  {selectedAppraisal.submittedAt && (
                    <div className="col-span-2">
                      <strong>Submitted:</strong> {formatDistanceToNow(new Date(selectedAppraisal.submittedAt), { addSuffix: true })}
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <Label htmlFor="rejection-reason">Rejection Reason (if rejecting)</Label>
                <Textarea
                  id="rejection-reason"
                  placeholder="Enter reason for rejection..."
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  className="mt-1"
                />
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsApprovalDialogOpen(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => handleApproval(selectedAppraisal.employeeId, 'reject')}
                  disabled={isSubmitting || !rejectionReason.trim()}
                >
                  {isSubmitting ? (
                    <>
                      <ClockIcon className="mr-2 h-4 w-4 animate-spin" />
                      Rejecting...
                    </>
                  ) : (
                    <>
                      <X className="mr-2 h-4 w-4" />
                      Reject
                    </>
                  )}
                </Button>
                <Button
                  onClick={() => handleApproval(selectedAppraisal.employeeId, 'approve')}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <ClockIcon className="mr-2 h-4 w-4 animate-spin" />
                      Approving...
                    </>
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Approve
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  )
}