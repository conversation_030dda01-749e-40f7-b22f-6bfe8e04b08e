'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  User, 
  Shield, 
  Bell, 
  Lock, 
  Mail,
  Calendar,
  Save,
  Settings
} from 'lucide-react'
import { AdminProfile, AdminPermissions } from '@/lib/admin-data'
import { useState } from 'react'
import { toast } from 'sonner'

interface Props {
  admin: AdminProfile
  permissions: AdminPermissions
}

export function AdminSettings({ admin, permissions }: Props) {
  const [profile, setProfile] = useState({
    fullName: admin.fullName,
    email: admin.email,
  })
  
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    appraisalReminders: true,
    weeklyReports: false,
    systemUpdates: true,
  })

  const handleSaveProfile = () => {
    // TODO: Implement save profile functionality
    toast.success('Profile updated successfully')
  }

  const handleSaveNotifications = () => {
    // TODO: Implement save notification settings
    toast.success('Notification settings updated')
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Admin Settings</h2>
        <p className="text-muted-foreground">
          Manage your admin profile and preferences
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Profile Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profile Information
            </CardTitle>
            <CardDescription>
              Update your personal information and contact details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="fullName">Full Name</Label>
              <Input
                id="fullName"
                value={profile.fullName}
                onChange={(e) => setProfile({ ...profile, fullName: e.target.value })}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={profile.email}
                onChange={(e) => setProfile({ ...profile, email: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label>Role</Label>
              <div className="flex items-center gap-2">
                <Badge 
                  variant={admin.role === 'super-admin' ? 'default' : 'secondary'}
                  className="flex items-center gap-1"
                >
                  <Shield className="h-3 w-3" />
                  {admin.role === 'super-admin' ? 'Super Admin' : 'Admin'}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  (Cannot be changed)
                </span>
              </div>
            </div>

            {admin.supervisorName && (
              <div className="space-y-2">
                <Label>Supervisor</Label>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{admin.supervisorName}</span>
                </div>
              </div>
            )}

            <Button onClick={handleSaveProfile} className="w-full">
              <Save className="h-4 w-4 mr-2" />
              Save Profile
            </Button>
          </CardContent>
        </Card>

        {/* Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Permissions & Access
            </CardTitle>
            <CardDescription>
              Your current permissions and access levels
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Manage Users</span>
                </div>
                <Badge variant={permissions.canManageUsers ? 'default' : 'secondary'}>
                  {permissions.canManageUsers ? 'Granted' : 'Denied'}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">View Reports</span>
                </div>
                <Badge variant={permissions.canViewReports ? 'default' : 'secondary'}>
                  {permissions.canViewReports ? 'Granted' : 'Denied'}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Export Data</span>
                </div>
                <Badge variant={permissions.canExportData ? 'default' : 'secondary'}>
                  {permissions.canExportData ? 'Granted' : 'Denied'}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Manage Settings</span>
                </div>
                <Badge variant={permissions.canManageSettings ? 'default' : 'secondary'}>
                  {permissions.canManageSettings ? 'Granted' : 'Denied'}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">View All Admins</span>
                </div>
                <Badge variant={permissions.canViewAllAdmins ? 'default' : 'secondary'}>
                  {permissions.canViewAllAdmins ? 'Granted' : 'Denied'}
                </Badge>
              </div>
            </div>

            <Separator />
            
            <div className="text-xs text-muted-foreground">
              Permissions are managed by your supervisor and cannot be changed from this interface.
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Preferences
          </CardTitle>
          <CardDescription>
            Configure how you receive notifications and updates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Email Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive notifications via email
                </p>
              </div>
              <Switch
                checked={notificationSettings.emailNotifications}
                onCheckedChange={(checked) =>
                  setNotificationSettings({ ...notificationSettings, emailNotifications: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Appraisal Reminders</Label>
                <p className="text-sm text-muted-foreground">
                  Get reminded about pending appraisals
                </p>
              </div>
              <Switch
                checked={notificationSettings.appraisalReminders}
                onCheckedChange={(checked) =>
                  setNotificationSettings({ ...notificationSettings, appraisalReminders: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Weekly Reports</Label>
                <p className="text-sm text-muted-foreground">
                  Receive weekly summary reports
                </p>
              </div>
              <Switch
                checked={notificationSettings.weeklyReports}
                onCheckedChange={(checked) =>
                  setNotificationSettings({ ...notificationSettings, weeklyReports: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>System Updates</Label>
                <p className="text-sm text-muted-foreground">
                  Get notified about system updates and maintenance
                </p>
              </div>
              <Switch
                checked={notificationSettings.systemUpdates}
                onCheckedChange={(checked) =>
                  setNotificationSettings({ ...notificationSettings, systemUpdates: checked })
                }
              />
            </div>
          </div>

          <Button onClick={handleSaveNotifications} className="w-full">
            <Save className="h-4 w-4 mr-2" />
            Save Notification Settings
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}