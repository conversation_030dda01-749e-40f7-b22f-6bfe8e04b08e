"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  MessageSquare, 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  MoreHorizontal,
  Eye,
  UserCheck,
  TrendingUp,
  Calendar,
  Filter
} from "lucide-react"
import type { EmployeeFeedback, FeedbackStatistics } from "@/lib/types"

interface HRFeedbackDashboardProps {
  pendingFeedback: EmployeeFeedback[]
  statistics: FeedbackStatistics | null
}

const priorityColors = {
  low: 'bg-gray-100 text-gray-800',
  medium: 'bg-blue-100 text-blue-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800'
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  under_review: 'bg-blue-100 text-blue-800',
  investigating: 'bg-purple-100 text-purple-800',
  resolved: 'bg-green-100 text-green-800',
  closed: 'bg-gray-100 text-gray-800',
  escalated: 'bg-red-100 text-red-800'
}

const typeIcons = {
  complaint: AlertTriangle,
  suggestion: MessageSquare,
  recognition: CheckCircle,
  concern: Clock,
  initiative: TrendingUp,
  general: MessageSquare
}

export function HRFeedbackDashboard({ pendingFeedback, statistics }: HRFeedbackDashboardProps) {
  const [selectedFilter, setSelectedFilter] = useState<string>('all')

  const filteredFeedback = pendingFeedback.filter(feedback => {
    if (selectedFilter === 'all') return true
    if (selectedFilter === 'urgent') return feedback.priority === 'urgent'
    if (selectedFilter === 'complaints') return feedback.feedbackType === 'complaint'
    return feedback.feedbackType === selectedFilter
  })

  const handleViewFeedback = (feedbackId: string) => {
    // Navigate to detailed feedback view
    window.location.href = `/dashboard/hr/feedback/${feedbackId}`
  }

  const handleUpdateStatus = async (feedbackId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/feedback/${feedbackId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        // Refresh the page or update state
        window.location.reload()
      }
    } catch (error) {
      console.error('Error updating status:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      {statistics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.totalFeedback}</div>
              <p className="text-xs text-muted-foreground">
                Last 30 days
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{statistics.pendingCount}</div>
              <p className="text-xs text-muted-foreground">
                Awaiting action
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Under Review</CardTitle>
              <UserCheck className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{statistics.underReviewCount}</div>
              <p className="text-xs text-muted-foreground">
                Being processed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Resolution</CardTitle>
              <Calendar className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {statistics.avgResolutionDays.toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">
                Days to resolve
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Dashboard */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Feedback Management
              </CardTitle>
              <CardDescription>
                Review and manage employee feedback submissions
              </CardDescription>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Filter by</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setSelectedFilter('all')}>
                  All Feedback
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedFilter('urgent')}>
                  Urgent Priority
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedFilter('complaints')}>
                  Complaints Only
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedFilter('suggestion')}>
                  Suggestions
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setSelectedFilter('recognition')}>
                  Recognition
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        <CardContent>
          {filteredFeedback.length === 0 ? (
            <div className="text-center py-12">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No feedback to review</h3>
              <p className="text-muted-foreground">
                All feedback has been processed or no feedback matches your filter.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>From</TableHead>
                    <TableHead>About</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredFeedback.map((feedback) => {
                    const TypeIcon = typeIcons[feedback.feedbackType] || MessageSquare
                    const daysPending = Math.floor(
                      (new Date().getTime() - new Date(feedback.createdAt).getTime()) / (1000 * 60 * 60 * 24)
                    )

                    return (
                      <TableRow key={feedback.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <TypeIcon className="h-4 w-4 text-muted-foreground" />
                            <span className="capitalize">{feedback.feedbackType}</span>
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          {feedback.subject}
                        </TableCell>
                        <TableCell>
                          {feedback.submitterName || 'Anonymous'}
                        </TableCell>
                        <TableCell>
                          {feedback.targetEmployeeName || 'General'}
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant="outline" 
                            className={priorityColors[feedback.priority]}
                          >
                            {feedback.priority}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant="outline" 
                            className={statusColors[feedback.status]}
                          >
                            {feedback.status.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(feedback.createdAt).toLocaleDateString()}
                            <div className="text-xs text-muted-foreground">
                              {daysPending} days ago
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewFeedback(feedback.id)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleUpdateStatus(feedback.id, 'under_review')}
                              >
                                Mark Under Review
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleUpdateStatus(feedback.id, 'investigating')}
                              >
                                Start Investigation
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleUpdateStatus(feedback.id, 'resolved')}
                              >
                                Mark Resolved
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
