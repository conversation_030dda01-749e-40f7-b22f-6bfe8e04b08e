"use client"

import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { appraisalQuestions } from "@/lib/constants/appraisal-questions"
import { readinessPromotionOptions } from "@/lib/constants/appraisal-ratings"
import type { AppraisalFormData, AppraisalInputChangeHandler, FormError } from "@/components/appraisal/types"

type ReadinessCompensationSectionProps = {
  formData: AppraisalFormData
  onInputChange: AppraisalInputChangeHandler
  disabled: boolean
  errors: {
    readinessPromotion?: FormError
    readinessComment?: FormError
    compensationRecommendation?: FormError
  }
}

export function ReadinessCompensationSection({
  formData,
  onInputChange,
  disabled,
  errors,
}: ReadinessCompensationSectionProps) {
  return (
    <div className="space-y-8 border-t pt-8">
      <h3 className="text-lg font-semibold">Readiness & Compensation</h3>

      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium">{appraisalQuestions.readinessPromotion} <span className="text-red-500">*</span></Label>
        </div>
        <RadioGroup
          value={formData.readinessPromotion ?? ""}
          onValueChange={(value) => onInputChange("readinessPromotion", value as "strong-yes" | "yes-with-reservations" | "no-not-yet")}
          className="flex flex-col space-y-2"
          disabled={disabled}
        >
          {readinessPromotionOptions.map((option) => (
            <div key={option.value} className="flex items-center space-x-2">
              <RadioGroupItem value={option.value} id={`readiness-${option.value}`} />
              <Label htmlFor={`readiness-${option.value}`} className="text-sm">{option.label}</Label>
            </div>
          ))}
        </RadioGroup>
        {errors.readinessPromotion?.message && (
          <p className="text-sm text-red-500">{errors.readinessPromotion.message}</p>
        )}

        <div className="space-y-2">
          <Label htmlFor="readinessComment">Comment: <span className="text-red-500">*</span></Label>
          <Textarea
            id="readinessComment"
            value={formData.readinessComment}
            onChange={(e) => onInputChange("readinessComment", e.target.value)}
            placeholder="Explain your assessment of their readiness for promotion or additional responsibility..."
            rows={4}
            disabled={disabled}
          />
          {errors.readinessComment?.message && (
            <p className="text-sm text-red-500">{errors.readinessComment.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="compensationRecommendation">💰 {appraisalQuestions.compensationRecommendation} <span className="text-red-500">*</span></Label>
        <Textarea
          id="compensationRecommendation"
          value={formData.compensationRecommendation}
          onChange={(e) => onInputChange("compensationRecommendation", e.target.value)}
          placeholder="Should this employee receive full salary adjustment / bonus / raise, or should it be adjusted based on performance or attendance?"
          rows={4}
          disabled={disabled}
        />
        {errors.compensationRecommendation?.message && (
          <p className="text-sm text-red-500">{errors.compensationRecommendation.message}</p>
        )}
      </div>
    </div>
  )
}