'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  Users, 
  FileText, 
  CheckCircle, 
  Clock, 
  Building, 
  UserCheck,
  TrendingUp,
  Shield,
  Settings,
  Download
} from 'lucide-react'
import { AdminProfile, AdminPermissions } from '@/lib/admin-data'
import { AuthUser } from '@/lib/auth'
import { AdminStatsCards } from './admin-stats-cards'
import { AdminManagement } from './admin-management'
import { AdminReports } from './admin-reports'
import { AdminSettings } from './admin-settings'

interface Props {
  admin: AdminProfile
  permissions: AdminPermissions
  currentUser: AuthUser
  stats?: any
  allAdmins?: AdminProfile[]
}

export function AdminDashboard({ admin, permissions, currentUser, stats, allAdmins = [] }: Props) {
  const isSuperAdmin = currentUser?.role === 'super-admin'
  const isOwner = currentUser?.id === admin.id
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {admin.fullName}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge 
            variant={admin.role === 'super-admin' ? 'default' : 'secondary'}
            className="flex items-center gap-1"
          >
            <Shield className="h-3 w-3" />
            {admin.role === 'super-admin' ? 'Super Admin' : 'Admin'}
          </Badge>
          {admin.supervisorName && (
            <Badge variant="outline" className="text-xs">
              Reports to: {admin.supervisorName}
            </Badge>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <AdminStatsCards stats={stats} />

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          {isSuperAdmin && (
            <TabsTrigger value="management">Admin Management</TabsTrigger>
          )}
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Employees
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">15</div>
                <p className="text-xs text-muted-foreground">
                  +2 from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Active Appraisals
                </CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8</div>
                <p className="text-xs text-muted-foreground">
                  In progress this month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Completed
                </CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">
                  85.7% completion rate
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Pending
                </CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">
                  Due this week
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest actions and updates in your admin area
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Appraisal submitted</p>
                    <p className="text-xs text-muted-foreground">
                      John Smith completed monthly review - 2 hours ago
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">New employee added</p>
                    <p className="text-xs text-muted-foreground">
                      Sarah Johnson joined Engineering dept - 1 day ago
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                  <div className="flex-1">
                    <p className="text-sm font-medium">Department updated</p>
                    <p className="text-xs text-muted-foreground">
                      Marketing department structure changed - 3 days ago
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports">
          <AdminReports permissions={permissions} />
        </TabsContent>

        {isSuperAdmin && (
          <TabsContent value="management">
            <AdminManagement currentUser={currentUser} admins={allAdmins} />
          </TabsContent>
        )}

        <TabsContent value="settings">
          <AdminSettings admin={admin} permissions={permissions} />
        </TabsContent>
      </Tabs>
    </div>
  )
}