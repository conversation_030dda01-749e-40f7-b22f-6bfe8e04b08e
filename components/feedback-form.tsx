"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  MessageSquare, 
  AlertTriangle, 
  Lightbulb, 
  Heart, 
  Flag, 
  FileText,
  Eye,
  EyeOff,
  Send,
  Loader2
} from "lucide-react"
import { toast } from "sonner"
import type { FeedbackType, FeedbackCate<PERSON><PERSON>, FeedbackPriority } from "@/lib/types"

interface EmployeeForFeedback {
  id: string
  fullName: string
  departmentName: string
}

const feedbackFormSchema = z.object({
  targetEmployeeId: z.string().optional(),
  feedbackType: z.enum(['complaint', 'suggestion', 'recognition', 'concern', 'initiative', 'general']),
  category: z.enum(['performance', 'behavior', 'communication', 'teamwork', 'leadership', 'process', 'other']).optional(),
  subject: z.string().min(5, "Subject must be at least 5 characters").max(255, "Subject must be less than 255 characters"),
  message: z.string().min(20, "Message must be at least 20 characters").max(2000, "Message must be less than 2000 characters"),
  isAnonymous: z.boolean(),
  priority: z.enum(['low', 'medium', 'high', 'urgent'])
})

type FeedbackFormValues = z.infer<typeof feedbackFormSchema>

interface FeedbackFormProps {
  employees: EmployeeForFeedback[]
  onSubmitSuccess?: () => void
}

const feedbackTypeOptions = [
  { value: 'complaint', label: 'Complaint', icon: AlertTriangle, color: 'text-red-600', description: 'Report an issue or problem' },
  { value: 'suggestion', label: 'Suggestion', icon: Lightbulb, color: 'text-yellow-600', description: 'Propose an improvement' },
  { value: 'recognition', label: 'Recognition', icon: Heart, color: 'text-green-600', description: 'Acknowledge good work' },
  { value: 'concern', label: 'Concern', icon: Flag, color: 'text-orange-600', description: 'Express a worry or concern' },
  { value: 'initiative', label: 'Initiative', icon: FileText, color: 'text-blue-600', description: 'Share an initiative or project' },
  { value: 'general', label: 'General', icon: MessageSquare, color: 'text-gray-600', description: 'General feedback' }
] as const

const categoryOptions = [
  { value: 'performance', label: 'Performance' },
  { value: 'behavior', label: 'Behavior' },
  { value: 'communication', label: 'Communication' },
  { value: 'teamwork', label: 'Teamwork' },
  { value: 'leadership', label: 'Leadership' },
  { value: 'process', label: 'Process' },
  { value: 'other', label: 'Other' }
] as const

const priorityOptions = [
  { value: 'low', label: 'Low', color: 'bg-gray-100 text-gray-800' },
  { value: 'medium', label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  { value: 'high', label: 'High', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' }
] as const

export function FeedbackForm({ employees, onSubmitSuccess }: FeedbackFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<FeedbackFormValues>({
    resolver: zodResolver(feedbackFormSchema),
    defaultValues: {
      targetEmployeeId: '',
      feedbackType: 'general',
      category: undefined,
      subject: '',
      message: '',
      isAnonymous: false,
      priority: 'medium'
    }
  })

  const selectedType = form.watch('feedbackType')
  const isAnonymous = form.watch('isAnonymous')
  const selectedPriority = form.watch('priority')

  const selectedTypeOption = feedbackTypeOptions.find(opt => opt.value === selectedType)
  const selectedPriorityOption = priorityOptions.find(opt => opt.value === selectedPriority)

  async function onSubmit(data: FeedbackFormValues) {
    setIsSubmitting(true)
    try {
      const response = await fetch('/api/feedback/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          targetEmployeeId: data.targetEmployeeId || null,
          feedbackType: data.feedbackType,
          category: data.category || null,
          subject: data.subject,
          message: data.message,
          isAnonymous: data.isAnonymous,
          priority: data.priority
        })
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Feedback submitted successfully! HR will review it shortly.')
        form.reset()
        onSubmitSuccess?.()
      } else {
        toast.error(result.error || 'Failed to submit feedback')
      }
    } catch (error) {
      console.error('Error submitting feedback:', error)
      toast.error('An unexpected error occurred. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Submit Feedback
        </CardTitle>
        <CardDescription>
          Share your feedback, suggestions, or concerns with HR. All submissions are treated confidentially.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Feedback Type */}
            <FormField
              control={form.control}
              name="feedbackType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Feedback Type</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {feedbackTypeOptions.map((option) => {
                        const Icon = option.icon
                        const isSelected = field.value === option.value
                        return (
                          <div
                            key={option.value}
                            className={`p-3 border rounded-lg cursor-pointer transition-all ${
                              isSelected 
                                ? 'border-primary bg-primary/5 ring-2 ring-primary/20' 
                                : 'border-border hover:border-primary/50'
                            }`}
                            onClick={() => field.onChange(option.value)}
                          >
                            <div className="flex items-center gap-2 mb-1">
                              <Icon className={`h-4 w-4 ${option.color}`} />
                              <span className="font-medium text-sm">{option.label}</span>
                            </div>
                            <p className="text-xs text-muted-foreground">{option.description}</p>
                          </div>
                        )
                      })}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Target Employee */}
            <FormField
              control={form.control}
              name="targetEmployeeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>About Employee (Optional)</FormLabel>
                  <FormControl>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select an employee (leave blank for general feedback)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">General Feedback (No specific employee)</SelectItem>
                        {employees.map((employee) => (
                          <SelectItem key={employee.id} value={employee.id}>
                            {employee.fullName} - {employee.departmentName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormDescription>
                    Choose an employee if your feedback is about a specific person
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Category and Priority Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category (Optional)</FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                        <SelectContent>
                          {categoryOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priority"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Priority</FormLabel>
                    <FormControl>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {priorityOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className={option.color}>
                                  {option.label}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Subject */}
            <FormField
              control={form.control}
              name="subject"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Subject</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      placeholder="Brief summary of your feedback"
                      maxLength={255}
                    />
                  </FormControl>
                  <FormDescription>
                    {field.value.length}/255 characters
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Message */}
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message</FormLabel>
                  <FormControl>
                    <Textarea 
                      {...field} 
                      placeholder="Provide detailed information about your feedback..."
                      rows={6}
                      maxLength={2000}
                    />
                  </FormControl>
                  <FormDescription>
                    {field.value.length}/2000 characters
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Anonymous Option */}
            <FormField
              control={form.control}
              name="isAnonymous"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base flex items-center gap-2">
                      {isAnonymous ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      Submit Anonymously
                    </FormLabel>
                    <FormDescription>
                      Your identity will be hidden from the feedback. HR may not be able to follow up directly.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Summary */}
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Submission Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <span>Type:</span>
                  {selectedTypeOption && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <selectedTypeOption.icon className={`h-3 w-3 ${selectedTypeOption.color}`} />
                      {selectedTypeOption.label}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span>Priority:</span>
                  {selectedPriorityOption && (
                    <Badge variant="outline" className={selectedPriorityOption.color}>
                      {selectedPriorityOption.label}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span>Anonymous:</span>
                  <Badge variant={isAnonymous ? "secondary" : "outline"}>
                    {isAnonymous ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="w-full"
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Submit Feedback
                </>
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
