'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Download, 
  FileText, 
  TrendingUp, 
  Users, 
  Calendar,
  BarChart3
} from 'lucide-react'
import { AdminPermissions } from '@/lib/admin-data'
import { toast } from 'sonner'

interface Props {
  permissions: AdminPermissions
}

export function AdminReports({ permissions }: Props) {
  const handleExportReport = (reportType: string) => {
    if (!permissions.canExportData) {
      toast.error('You do not have permission to export data')
      return
    }
    
    // TODO: Implement actual export functionality
    toast.success(`Exporting ${reportType} report...`)
  }

  const reports = [
    {
      id: 'employee-summary',
      title: 'Employee Summary Report',
      description: 'Complete overview of all employees, departments, and their current status',
      icon: Users,
      lastGenerated: '2 hours ago',
      format: 'CSV, PDF',
      canExport: permissions.canExportData
    },
    {
      id: 'appraisal-summary',
      title: 'Appraisal Summary Report',
      description: 'Monthly appraisal completion rates and performance metrics',
      icon: FileText,
      lastGenerated: '1 day ago',
      format: 'CSV, PDF',
      canExport: permissions.canExportData
    },
    {
      id: 'department-analytics',
      title: 'Department Analytics',
      description: 'Performance metrics and trends by department',
      icon: BarChart3,
      lastGenerated: '3 days ago',
      format: 'PDF',
      canExport: permissions.canExportData
    },
    {
      id: 'monthly-trends',
      title: 'Monthly Trends Report',
      description: 'Month-over-month performance and completion trends',
      icon: TrendingUp,
      lastGenerated: '1 week ago',
      format: 'CSV, PDF',
      canExport: permissions.canExportData
    }
  ]

  const recentAppraisals = [
    {
      employee: 'John Smith',
      department: 'Engineering',
      manager: 'Sarah Johnson',
      submittedAt: '2 hours ago',
      status: 'submitted'
    },
    {
      employee: 'Alice Brown',
      department: 'Product',
      manager: 'Mike Wilson',
      submittedAt: '5 hours ago',
      status: 'submitted'
    },
    {
      employee: 'Bob Davis',
      department: 'Marketing',
      manager: 'Lisa Chen',
      submittedAt: '1 day ago',
      status: 'submitted'
    }
  ]

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Reports & Analytics</h2>
        <p className="text-muted-foreground">
          Generate and export reports for organizational insights
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">85.7%</div>
            <p className="text-xs text-muted-foreground">Completion rate</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Avg. Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12m</div>
            <p className="text-xs text-muted-foreground">Per appraisal</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Top Dept</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Eng</div>
            <p className="text-xs text-muted-foreground">95% completion</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">Need attention</p>
          </CardContent>
        </Card>
      </div>

      {/* Available Reports */}
      <Card>
        <CardHeader>
          <CardTitle>Available Reports</CardTitle>
          <CardDescription>
            Generate and download reports for data analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reports.map((report) => {
              const Icon = report.icon
              return (
                <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Icon className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-medium">{report.title}</h4>
                      <p className="text-sm text-muted-foreground">{report.description}</p>
                      <div className="flex items-center gap-4 mt-2">
                        <span className="text-xs text-muted-foreground">
                          Last generated: {report.lastGenerated}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Format: {report.format}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleExportReport(report.title)}
                      disabled={!report.canExport}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Appraisals</CardTitle>
          <CardDescription>
            Latest submitted appraisals across all departments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Department</TableHead>
                <TableHead>Manager</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentAppraisals.map((appraisal, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{appraisal.employee}</TableCell>
                  <TableCell>{appraisal.department}</TableCell>
                  <TableCell>{appraisal.manager}</TableCell>
                  <TableCell>{appraisal.submittedAt}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className="text-green-600">
                      {appraisal.status}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}