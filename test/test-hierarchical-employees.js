// Test script to verify hierarchical employee functionality
// This script tests the new getEmployeesForManager function

import { getEmployeesForManager } from '../lib/data/employees.js'

async function testHierarchicalEmployees() {
  console.log('🧪 Testing Hierarchical Employee Functionality')
  console.log('=' .repeat(50))
  
  try {
    // Test with <PERSON>'s user ID
    const monaUserId = 'user_2zgIPJfN1J3mcwjHH27wFQT9NQW'
    console.log(`\n🔍 Testing with <PERSON>'s user ID: ${monaUserId}`)
    
    const employees = await getEmployeesForManager(monaUserId)
    
    console.log(`\n✅ Found ${employees.length} employees in Mona's hierarchy`)
    
    // Group by hierarchy level (we can infer this from manager relationships)
    const directReports = employees.filter(emp => emp.managerId === monaUserId)
    const indirectReports = employees.filter(emp => emp.managerId !== monaUserId)
    
    console.log(`\n📊 Hierarchy Breakdown:`)
    console.log(`   Direct Reports (Level 1): ${directReports.length}`)
    console.log(`   Indirect Reports (Level 2+): ${indirectReports.length}`)
    
    console.log(`\n👥 Direct Reports:`)
    directReports.forEach(emp => {
      console.log(`   - ${emp.fullName} (ID: ${emp.id})`)
    })
    
    console.log(`\n🌳 Indirect Reports:`)
    indirectReports.forEach(emp => {
      console.log(`   - ${emp.fullName} (managed by: ${emp.managerName})`)
    })
    
    // Verify specific employees we expect to see
    const expectedDirectReports = [
      'Mia Owaini',
      'Mariangelica Angulo',
      'Francesco Paolo Oddo',
      'Althene Kay Foliente'
    ]
    
    const expectedIndirectReports = [
      'Alexandra Narain Valero Hernandez', // Mia's employee
      'Ana Karina Superlano', // Mia's employee
      'Alberto Velandia', // Mariangelica's employee
      'Janet Bahouth' // Mariangelica's employee
    ]
    
    console.log(`\n🎯 Verification:`)
    
    expectedDirectReports.forEach(name => {
      const found = directReports.find(emp => emp.fullName === name)
      console.log(`   ${found ? '✅' : '❌'} Direct: ${name}`)
    })
    
    expectedIndirectReports.forEach(name => {
      const found = indirectReports.find(emp => emp.fullName === name)
      console.log(`   ${found ? '✅' : '❌'} Indirect: ${name}`)
    })
    
    console.log(`\n🎉 Test completed successfully!`)
    
  } catch (error) {
    console.error('🚨 Test failed:', error)
  }
}

// Run the test
testHierarchicalEmployees()
