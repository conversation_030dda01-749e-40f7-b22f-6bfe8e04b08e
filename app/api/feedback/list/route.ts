import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { db } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Fetching feedback list...')
    
    const { userId } = await auth()
    if (!userId) {
      console.log('❌ Unauthorized feedback list request')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has HR or admin permissions
    const userRole = await db.getUserRole(userId)
    if (!userRole || !['hr-admin', 'super-admin'].includes(userRole.role)) {
      console.log('❌ Insufficient permissions for feedback list:', userRole?.role)
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const feedbackType = searchParams.get('feedbackType')
    const priority = searchParams.get('priority')
    const limit = searchParams.get('limit')

    console.log('📋 Fetching feedback with filters:', {
      status,
      feedbackType,
      priority,
      limit
    })

    // Build filters object
    const filters: any = {}
    if (status) filters.status = status
    if (feedbackType) filters.feedbackType = feedbackType
    if (priority) filters.priority = priority
    if (limit) filters.limit = parseInt(limit)

    // Fetch feedback
    const feedback = await db.getFeedback(filters)

    console.log('✅ Feedback fetched successfully:', feedback.length, 'items')

    return NextResponse.json({
      success: true,
      feedback,
      count: feedback.length
    })

  } catch (error) {
    console.error('❌ Error fetching feedback:', error)
    return NextResponse.json({
      error: 'Failed to fetch feedback'
    }, { status: 500 })
  }
}
