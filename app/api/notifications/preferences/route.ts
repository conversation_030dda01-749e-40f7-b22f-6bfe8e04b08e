import { NextRequest, NextResponse } from 'next/server'
import { notificationService } from '@/lib/services/notifications'
import { getCurrentUser } from '@/lib/auth'

/**
 * GET - Fetch notification preferences for current user
 */
export async function GET(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const preferences = await notificationService.getNotificationPreferences(currentUser.id)
    
    return NextResponse.json({ 
      success: true, 
      preferences 
    })

  } catch (error) {
    console.error('Error fetching notification preferences:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch notification preferences' 
    }, { status: 500 })
  }
}

/**
 * POST - Update notification preferences for current user
 */
export async function POST(request: NextRequest) {
  try {
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { email_enabled, reminder_days_before, reminder_time } = body

    // Validate input
    if (typeof email_enabled !== 'boolean') {
      return NextResponse.json({ error: 'email_enabled must be a boolean' }, { status: 400 })
    }

    if (reminder_days_before && (reminder_days_before < 1 || reminder_days_before > 14)) {
      return NextResponse.json({ error: 'reminder_days_before must be between 1 and 14' }, { status: 400 })
    }

    if (reminder_time && !/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/.test(reminder_time)) {
      return NextResponse.json({ error: 'reminder_time must be in HH:MM:SS format' }, { status: 400 })
    }

    const success = await notificationService.updateNotificationPreferences(currentUser.id, {
      email_enabled,
      reminder_days_before,
      reminder_time
    })

    if (success) {
      return NextResponse.json({ 
        success: true, 
        message: 'Notification preferences updated successfully' 
      })
    } else {
      return NextResponse.json({ 
        error: 'Failed to update notification preferences' 
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error updating notification preferences:', error)
    return NextResponse.json({ 
      error: 'Failed to update notification preferences' 
    }, { status: 500 })
  }
}
