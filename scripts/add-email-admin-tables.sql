-- Email Admin Configuration Tables
-- This script adds tables for superadmin email configuration management

-- Email configuration settings table
CREATE TABLE IF NOT EXISTS appy_email_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  setting_key VARCHAR(100) NOT NULL UNIQUE,
  setting_value TEXT NOT NULL,
  setting_type VARCHAR(50) NOT NULL DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES appy_managers(user_id)
);

-- Email templates table for customizable templates
CREATE TABLE IF NOT EXISTS appy_email_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_key VARCHAR(100) NOT NULL UNIQUE, -- 'appraisal_reminder', 'test_email', etc.
  template_name VARCHAR(200) NOT NULL,
  subject_template TEXT NOT NULL,
  html_template TEXT NOT NULL,
  text_template TEXT NOT NULL,
  available_variables JSON DEFAULT '[]', -- Array of variable names like ["manager_name", "count"]
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES appy_managers(user_id)
);

-- Email configuration audit log
CREATE TABLE IF NOT EXISTS appy_email_config_audit (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  action VARCHAR(50) NOT NULL, -- 'create', 'update', 'delete', 'test'
  table_name VARCHAR(100) NOT NULL, -- 'appy_email_settings', 'appy_email_templates'
  record_id UUID NOT NULL,
  old_values JSON,
  new_values JSON,
  changed_by UUID REFERENCES appy_managers(user_id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default email settings
INSERT INTO appy_email_settings (setting_key, setting_value, setting_type, description) 
VALUES 
  ('from_email', '<EMAIL>', 'string', 'Default sender email address'),
  ('from_name', 'Appraisal System', 'string', 'Display name for sender'),
  ('reply_to_email', '<EMAIL>', 'string', 'Reply-to email address'),
  ('test_email', '<EMAIL>', 'string', 'Email address for testing'),
  ('notifications_enabled', 'true', 'boolean', 'Global enable/disable for email notifications'),
  ('default_reminder_days', '4', 'number', 'Default number of days before period end to send reminders'),
  ('email_signature', 'Best regards,<br>The Appraisal System Team', 'string', 'Email signature template')
ON CONFLICT (setting_key) DO NOTHING;

-- Insert default appraisal reminder template
INSERT INTO appy_email_templates (
  template_key, 
  template_name, 
  subject_template, 
  html_template, 
  text_template,
  available_variables
) VALUES (
  'appraisal_reminder',
  'Employee Appraisal Reminder',
  '⏰ Reminder: {{count}} Pending Appraisal{{count_plural}} Due Soon',
  '<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Appraisal Reminder</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, ''Segoe UI'', Roboto, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .content { background: white; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
    .employee-list { background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 15px 0; }
    .button { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 15px 0; }
    .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 14px; color: #6c757d; }
    .urgent { color: #dc3545; font-weight: bold; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔔 Appraisal Reminder</h1>
      <p>Hello {{manager_name}},</p>
    </div>
    
    <div class="content">
      <p>This is a friendly reminder that you have <strong class="urgent">{{count}} pending employee appraisal{{count_plural}}</strong> that need to be completed by <strong>{{period_end_formatted}}</strong>.</p>
      
      <div class="employee-list">
        <h3>📋 Employees Awaiting Appraisal:</h3>
        <ul>
          {{employee_list}}
        </ul>
      </div>
      
      <p>Please complete these appraisals as soon as possible to ensure timely processing and payment for your team members.</p>
      
      <a href="{{dashboard_url}}" class="button">
        Complete Appraisals Now →
      </a>
      
      <p><small>💡 <strong>Tip:</strong> You can save drafts and return later to complete them. Each appraisal takes approximately 5-10 minutes to complete.</small></p>
    </div>
    
    <div class="footer">
      <p>This is an automated reminder from the Employee Appraisal System.</p>
      <p>If you have any questions, please contact HR or your system administrator.</p>
      <p>{{email_signature}}</p>
    </div>
  </div>
</body>
</html>',
  'Appraisal Reminder

Hello {{manager_name}},

This is a friendly reminder that you have {{count}} pending employee appraisal{{count_plural}} that need to be completed by {{period_end_formatted}}.

Employees Awaiting Appraisal:
{{employee_list_text}}

Please complete these appraisals as soon as possible to ensure timely processing and payment for your team members.

Access the appraisal system: {{dashboard_url}}

This is an automated reminder from the Employee Appraisal System.
If you have any questions, please contact HR or your system administrator.

{{email_signature}}',
  '["manager_name", "count", "count_plural", "period_end_formatted", "employee_list", "employee_list_text", "dashboard_url", "email_signature"]'
) ON CONFLICT (template_key) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_settings_key ON appy_email_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_email_templates_key ON appy_email_templates(template_key);
CREATE INDEX IF NOT EXISTS idx_email_templates_active ON appy_email_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_email_audit_created_at ON appy_email_config_audit(created_at);
CREATE INDEX IF NOT EXISTS idx_email_audit_table_record ON appy_email_config_audit(table_name, record_id);

-- Add comments for documentation
COMMENT ON TABLE appy_email_settings IS 'Stores configurable email system settings';
COMMENT ON TABLE appy_email_templates IS 'Stores customizable email templates with variable substitution';
COMMENT ON TABLE appy_email_config_audit IS 'Audit log for email configuration changes';

COMMENT ON COLUMN appy_email_settings.setting_key IS 'Unique identifier for the setting';
COMMENT ON COLUMN appy_email_settings.setting_type IS 'Data type: string, number, boolean, json';
COMMENT ON COLUMN appy_email_templates.template_key IS 'Unique identifier for the template';
COMMENT ON COLUMN appy_email_templates.available_variables IS 'JSON array of variable names available for substitution';