-- Debug script to check current database state
-- Run this to see what data exists

-- Check departments
SELECT 'DEPARTMENTS' as table_name, count(*) as count FROM appy_departments;
SELECT * FROM appy_departments ORDER BY name;

-- Check managers
SELECT 'MANAGERS' as table_name, count(*) as count FROM appy_managers;
SELECT * FROM appy_managers ORDER BY full_name;

-- Check user roles
SELECT 'USER_ROLES' as table_name, count(*) as count FROM appy_user_roles;
SELECT * FROM appy_user_roles;

-- Check employees
SELECT 'EMPLOYEES' as table_name, count(*) as count FROM appy_employees;
SELECT 
  id, 
  full_name, 
  compensation, 
  rate, 
  department_id, 
  manager_id, 
  active 
FROM appy_employees 
ORDER BY full_name;

-- Check appraisal periods
SELECT 'APPRAISAL_PERIODS' as table_name, count(*) as count FROM appy_appraisal_periods;
SELECT * FROM appy_appraisal_periods ORDER BY start_date DESC;

-- Check appraisals
SELECT 'APPRAISALS' as table_name, count(*) as count FROM appy_appraisals;
SELECT 
  id,
  employee_id,
  period_id,
  manager_id,
  question_1,
  status,
  submitted_at,
  created_at
FROM appy_appraisals 
ORDER BY created_at DESC;

-- Check employees with department names
SELECT 
  e.id,
  e.full_name,
  e.compensation,
  e.rate,
  e.manager_id,
  d.name as department_name,
  m.full_name as manager_name
FROM appy_employees e
LEFT JOIN appy_departments d ON e.department_id = d.id
LEFT JOIN appy_managers m ON e.manager_id = m.user_id
WHERE e.active = true
ORDER BY e.full_name;
