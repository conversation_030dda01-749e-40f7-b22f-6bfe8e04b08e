# Database Testing Scripts

## Fix Team Performance Distribution

The Team Performance Distribution was showing "No data available" due to missing test data and some code mapping issues.

### What was fixed:

1. **Code Mapping**: Fixed the compensation/rate field mapping in `lib/data.ts`
2. **Enhanced Logging**: Added detailed console logging to debug performance stats
3. **Test Data**: Created proper test data with correct question_1 values

### To test the fix:

1. **Add test data to your database:**
   ```sql
   -- Run this in your Supabase SQL editor
   \i scripts/add-test-data.sql
   ```

2. **Test the performance stats function:**
   ```bash
   npx tsx scripts/test-performance-stats.ts
   ```

3. **Check the dashboard:**
   - Go to `/dashboard` 
   - You should now see the Team Performance Distribution chart with data
   - Check browser console for detailed debug logs

### Test Data Created:

- **<PERSON>** as manager (using Clerk ID: `user_2qGVhQKJhJZhJZhJZhJZhJ`)
- **4 employees** under <PERSON>:
  - <PERSON> (Engineering) - Submitted appraisal with "exceeds-expectations"
  - <PERSON> (Engineering) - Submitted appraisal with "meets-expectations"  
  - <PERSON> (Marketing) - Draft appraisal (pending status)
  - <PERSON> (Product) - No appraisal yet (not-started)
- **Active appraisal period** for July 2025

### Expected Results:

The Team Performance Distribution should show:
- **Total**: 4 employees
- **Exceeds Expectations**: 1 (Alice)
- **Meets Expectations**: 1 (Bob)
- **Below Expectations**: 0
- **Not Started**: 1 (David)
- **Drafts**: 1 (Carol)
- **Submitted**: 2 (Alice + Bob)

### Debug Console Logs:

Look for these log prefixes in the browser console:
- `📊 [PERF STATS]` - Performance stats function logs
- `📅 [PERF STATS]` - Period-related logs
- `📋 [PERF STATS]` - Appraisal data logs
- `👥 [PERF STATS]` - Employee summary logs

### If still not working:

1. Check if Francesco's Clerk user ID matches the one in the test data
2. Verify the database connection is working
3. Check if the active appraisal period exists
4. Run the test script to see detailed debug output
