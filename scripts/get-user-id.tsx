"use client"

// Component to display current user ID
// Add this to any page temporarily to get your Clerk user ID

import { useUser } from '@clerk/nextjs'

export function GetUserIdComponent() {
  const { user, isLoaded } = useUser()
  
  if (!isLoaded) {
    return <div>Loading...</div>
  }
  
  if (!user) {
    return <div>Not logged in</div>
  }
  
  return (
    <div className="p-4 bg-yellow-100 border border-yellow-400 rounded">
      <h3 className="font-bold text-lg mb-2">🔍 Your Clerk User ID:</h3>
      <code className="bg-gray-100 p-2 rounded block mb-2 text-sm">
        {user.id}
      </code>
      <p className="text-sm text-gray-600">
        Copy this ID and update the test data script with your actual Clerk user ID.
      </p>
      <div className="mt-2 text-xs">
        <p><strong>Name:</strong> {user.fullName}</p>
        <p><strong>Email:</strong> {user.primaryEmailAddress?.emailAddress}</p>
      </div>
    </div>
  )
}

// Instructions:
// 1. Import this component in any page: import { GetUserIdComponent } from '../scripts/get-user-id'
// 2. Add <GetUserIdComponent /> to the page
// 3. Copy your user ID
// 4. Update scripts/add-test-data.sql with your actual user ID
// 5. Remove this component when done
