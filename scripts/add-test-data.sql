-- Add test data for Team Performance Distribution
-- This creates the minimum data needed to test the performance stats

-- First, let's add <PERSON> as a manager (using your Clerk ID)
INSERT INTO appy_managers (user_id, full_name, email) 
VALUES ('user_2qGVhQKJhJZhJZh<PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>', '<PERSON>', '<EMAIL>')
ON CONFLICT (user_id) DO UPDATE SET 
  full_name = EXCLUDED.full_name,
  email = EXCLUDED.email;

-- Add <PERSON>'s role as manager
INSERT INTO appy_user_roles (user_id, role) 
VALUES ('user_2qGVhQKJhJZhJZhJZhJZhJ', 'manager')
ON CONFLICT (user_id) DO UPDATE SET role = EXCLUDED.role;

-- Create some departments if they don't exist
INSERT INTO appy_departments (name) VALUES ('Engineering') ON CONFLICT (name) DO NOTHING;
INSERT INTO appy_departments (name) VALUES ('Marketing') ON CONFLICT (name) DO NOTHING;
INSERT INTO appy_departments (name) VALUES ('Product') ON CONFLICT (name) DO NOTHING;

-- Get department IDs
DO $$
DECLARE
    eng_dept_id uuid;
    marketing_dept_id uuid;
    product_dept_id uuid;
    francesco_user_id text := 'user_2qGVhQKJhJZhJZhJZhJZhJ';
    current_period_id uuid;
    emp1_id uuid;
    emp2_id uuid;
    emp3_id uuid;
    emp4_id uuid;
BEGIN
    -- Get department IDs
    SELECT id INTO eng_dept_id FROM appy_departments WHERE name = 'Engineering';
    SELECT id INTO marketing_dept_id FROM appy_departments WHERE name = 'Marketing';
    SELECT id INTO product_dept_id FROM appy_departments WHERE name = 'Product';
    
    -- Create employees managed by Francesco
    INSERT INTO appy_employees (full_name, compensation, rate, department_id, manager_id) 
    VALUES 
        ('Alice Johnson', 85000, 'monthly', eng_dept_id, francesco_user_id),
        ('Bob Smith', 45, 'hourly', eng_dept_id, francesco_user_id),
        ('Carol Davis', 75000, 'monthly', marketing_dept_id, francesco_user_id),
        ('David Wilson', 50, 'hourly', product_dept_id, francesco_user_id)
    ON CONFLICT DO NOTHING
    RETURNING id INTO emp1_id, emp2_id, emp3_id, emp4_id;
    
    -- If employees already exist, get their IDs
    IF emp1_id IS NULL THEN
        SELECT id INTO emp1_id FROM appy_employees WHERE full_name = 'Alice Johnson' AND manager_id = francesco_user_id;
        SELECT id INTO emp2_id FROM appy_employees WHERE full_name = 'Bob Smith' AND manager_id = francesco_user_id;
        SELECT id INTO emp3_id FROM appy_employees WHERE full_name = 'Carol Davis' AND manager_id = francesco_user_id;
        SELECT id INTO emp4_id FROM appy_employees WHERE full_name = 'David Wilson' AND manager_id = francesco_user_id;
    END IF;
    
    -- Create an active appraisal period for current month
    INSERT INTO appy_appraisal_periods (name, start_date, end_date, status)
    VALUES (
        'July 2025 Appraisals',
        '2025-07-01',
        '2025-07-31',
        'active'
    )
    ON CONFLICT DO NOTHING;
    
    -- Get the current period ID
    SELECT id INTO current_period_id 
    FROM appy_appraisal_periods 
    WHERE status = 'active' 
    ORDER BY start_date DESC 
    LIMIT 1;
    
    -- Create some test appraisals with proper question_1 values
    -- Alice - Submitted with "exceeds-expectations"
    INSERT INTO appy_appraisals (employee_id, period_id, manager_id, question_1, question_2, question_3, question_4, question_5, status, submitted_at)
    VALUES (
        emp1_id, 
        current_period_id, 
        francesco_user_id, 
        'exceeds-expectations', 
        'true', 
        'Alice consistently delivers high-quality work and mentors junior developers.',
        'Led the migration project successfully, improved team productivity by 25%.',
        'Consider for senior developer promotion. Excellent technical and leadership skills.',
        'submitted',
        NOW() - INTERVAL '2 days'
    )
    ON CONFLICT (employee_id, period_id) DO UPDATE SET
        question_1 = EXCLUDED.question_1,
        question_2 = EXCLUDED.question_2,
        question_3 = EXCLUDED.question_3,
        question_4 = EXCLUDED.question_4,
        question_5 = EXCLUDED.question_5,
        status = EXCLUDED.status,
        submitted_at = EXCLUDED.submitted_at;
    
    -- Bob - Submitted with "meets-expectations"
    INSERT INTO appy_appraisals (employee_id, period_id, manager_id, question_1, question_2, question_3, question_4, question_5, status, submitted_at)
    VALUES (
        emp2_id, 
        current_period_id, 
        francesco_user_id, 
        'meets-expectations', 
        'true', 
        'Bob is reliable and completes tasks on time with good quality.',
        'Completed all assigned tickets, participated well in code reviews.',
        'Continue current trajectory. Consider additional training opportunities.',
        'submitted',
        NOW() - INTERVAL '1 day'
    )
    ON CONFLICT (employee_id, period_id) DO UPDATE SET
        question_1 = EXCLUDED.question_1,
        question_2 = EXCLUDED.question_2,
        question_3 = EXCLUDED.question_3,
        question_4 = EXCLUDED.question_4,
        question_5 = EXCLUDED.question_5,
        status = EXCLUDED.status,
        submitted_at = EXCLUDED.submitted_at;
    
    -- Carol - Draft (pending status)
    INSERT INTO appy_appraisals (employee_id, period_id, manager_id, question_1, question_2, status)
    VALUES (
        emp3_id, 
        current_period_id, 
        francesco_user_id, 
        'meets-expectations', 
        'true', 
        'pending'
    )
    ON CONFLICT (employee_id, period_id) DO UPDATE SET
        question_1 = EXCLUDED.question_1,
        question_2 = EXCLUDED.question_2,
        status = EXCLUDED.status;
    
    -- David - No appraisal yet (will show as "not-started")
    
    RAISE NOTICE 'Test data created successfully!';
    RAISE NOTICE 'Francesco user_id: %', francesco_user_id;
    RAISE NOTICE 'Current period: %', current_period_id;
    RAISE NOTICE 'Employees created: Alice (%), Bob (%), Carol (%), David (%)', emp1_id, emp2_id, emp3_id, emp4_id;
    
END $$;
