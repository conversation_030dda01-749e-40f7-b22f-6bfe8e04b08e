-- Migration: Add Email Notification System
-- This script adds tables and functionality for email notifications

-- Create notification preferences table
CREATE TABLE IF NOT EXISTS appy_notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR(255) NOT NULL, -- Clerk user ID
  email_enabled BOOLEAN DEFAULT true,
  reminder_days_before INTEGER DEFAULT 4, -- Days before month end to send reminder
  reminder_time TIME DEFAULT '09:00:00', -- Time of day to send reminders
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create notification log table
CREATE TABLE IF NOT EXISTS appy_notification_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR(255) NOT NULL, -- Clerk user ID
  notification_type VARCHAR(50) NOT NULL CHECK (notification_type IN ('appraisal_reminder', 'approval_pending', 'feedback_received')),
  subject VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  email_sent BO<PERSON>EAN DEFAULT false,
  email_sent_at TIMESTAMP WITH TIME ZONE,
  email_error TEXT,
  metadata JSONB, -- Store additional data like employee IDs, period info
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create scheduled notifications table
CREATE TABLE IF NOT EXISTS appy_scheduled_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR(255) NOT NULL, -- Clerk user ID
  notification_type VARCHAR(50) NOT NULL,
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  period_id UUID REFERENCES appy_appraisal_periods(id),
  metadata JSONB,
  processed BOOLEAN DEFAULT false,
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user ON appy_notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_log_user ON appy_notification_log(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_log_type ON appy_notification_log(notification_type);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_user ON appy_scheduled_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled ON appy_scheduled_notifications(scheduled_for);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_processed ON appy_scheduled_notifications(processed);

-- Insert default notification preferences for existing managers
INSERT INTO appy_notification_preferences (user_id, email_enabled, reminder_days_before, reminder_time)
SELECT DISTINCT user_id, true, 4, '09:00:00'
FROM appy_managers
WHERE user_id NOT IN (SELECT user_id FROM appy_notification_preferences);

-- Create function to get pending appraisals for a manager
CREATE OR REPLACE FUNCTION get_pending_appraisals_for_manager(manager_user_id VARCHAR(255), period_id UUID)
RETURNS TABLE (
  employee_id UUID,
  employee_name VARCHAR(255),
  department_name VARCHAR(255),
  days_remaining INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    e.id as employee_id,
    e.full_name as employee_name,
    d.name as department_name,
    EXTRACT(DAY FROM (ap.period_end - CURRENT_DATE))::INTEGER as days_remaining
  FROM appy_employees e
  JOIN appy_departments d ON e.department_id = d.id
  JOIN appy_appraisal_periods ap ON ap.id = period_id
  LEFT JOIN appy_appraisals a ON a.employee_id = e.id AND a.period_id = period_id
  WHERE e.manager_id = manager_user_id
    AND e.active = true
    AND ap.closed = false
    AND (a.id IS NULL OR a.status = 'draft')
  ORDER BY e.full_name;
END;
$$ LANGUAGE plpgsql;

-- Create function to schedule monthly reminder notifications
CREATE OR REPLACE FUNCTION schedule_monthly_reminders()
RETURNS INTEGER AS $$
DECLARE
  current_period RECORD;
  manager_record RECORD;
  reminder_date TIMESTAMP WITH TIME ZONE;
  notification_count INTEGER := 0;
BEGIN
  -- Get current active period
  SELECT * INTO current_period
  FROM appy_appraisal_periods
  WHERE closed = false
  ORDER BY period_end ASC
  LIMIT 1;
  
  IF current_period IS NULL THEN
    RETURN 0;
  END IF;
  
  -- Loop through all managers with notification preferences
  FOR manager_record IN
    SELECT m.user_id, m.full_name, m.email, np.reminder_days_before, np.reminder_time
    FROM appy_managers m
    JOIN appy_notification_preferences np ON m.user_id = np.user_id
    WHERE m.active = true AND np.email_enabled = true
  LOOP
    -- Calculate reminder date (X days before period end)
    reminder_date := (current_period.period_end - INTERVAL '1 day' * manager_record.reminder_days_before) + manager_record.reminder_time;
    
    -- Only schedule if reminder date is in the future and not already scheduled
    IF reminder_date > NOW() THEN
      INSERT INTO appy_scheduled_notifications (
        user_id,
        notification_type,
        scheduled_for,
        period_id,
        metadata
      )
      SELECT 
        manager_record.user_id,
        'appraisal_reminder',
        reminder_date,
        current_period.id,
        jsonb_build_object(
          'manager_name', manager_record.full_name,
          'manager_email', manager_record.email,
          'period_end', current_period.period_end
        )
      WHERE NOT EXISTS (
        SELECT 1 FROM appy_scheduled_notifications
        WHERE user_id = manager_record.user_id
          AND notification_type = 'appraisal_reminder'
          AND period_id = current_period.id
          AND processed = false
      );
      
      notification_count := notification_count + 1;
    END IF;
  END LOOP;
  
  RETURN notification_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to process scheduled notifications
CREATE OR REPLACE FUNCTION process_scheduled_notifications()
RETURNS INTEGER AS $$
DECLARE
  notification_record RECORD;
  pending_employees RECORD;
  email_subject VARCHAR(255);
  email_body TEXT;
  employee_list TEXT := '';
  processed_count INTEGER := 0;
BEGIN
  -- Process all due notifications
  FOR notification_record IN
    SELECT *
    FROM appy_scheduled_notifications
    WHERE scheduled_for <= NOW()
      AND processed = false
      AND notification_type = 'appraisal_reminder'
  LOOP
    -- Get pending appraisals for this manager
    employee_list := '';
    FOR pending_employees IN
      SELECT * FROM get_pending_appraisals_for_manager(
        notification_record.user_id,
        notification_record.period_id
      )
    LOOP
      employee_list := employee_list || '• ' || pending_employees.employee_name || 
                      ' (' || pending_employees.department_name || ')' || E'\n';
    END LOOP;
    
    -- Only send notification if there are pending appraisals
    IF employee_list != '' THEN
      email_subject := 'Reminder: Pending Employee Appraisals Due Soon';
      email_body := 'Dear ' || (notification_record.metadata->>'manager_name') || E',\n\n' ||
                   'This is a friendly reminder that the following employee appraisals are still pending and due by ' ||
                   (notification_record.metadata->>'period_end') || E':\n\n' ||
                   employee_list || E'\n' ||
                   'Please complete these appraisals as soon as possible to ensure timely processing.' || E'\n\n' ||
                   'You can access the appraisal system at: ' || current_setting('app.base_url', true) || '/dashboard' || E'\n\n' ||
                   'Best regards,' || E'\n' ||
                   'The Appraisal System';
      
      -- Log the notification
      INSERT INTO appy_notification_log (
        user_id,
        notification_type,
        subject,
        message,
        metadata
      ) VALUES (
        notification_record.user_id,
        'appraisal_reminder',
        email_subject,
        email_body,
        jsonb_build_object(
          'period_id', notification_record.period_id,
          'employee_count', (SELECT COUNT(*) FROM get_pending_appraisals_for_manager(notification_record.user_id, notification_record.period_id))
        )
      );
    END IF;
    
    -- Mark notification as processed
    UPDATE appy_scheduled_notifications
    SET processed = true, processed_at = NOW()
    WHERE id = notification_record.id;
    
    processed_count := processed_count + 1;
  END LOOP;
  
  RETURN processed_count;
END;
$$ LANGUAGE plpgsql;

-- Set application setting for base URL (to be updated in production)
SELECT set_config('app.base_url', 'http://localhost:3000', false);

COMMENT ON TABLE appy_notification_preferences IS 'User preferences for email notifications';
COMMENT ON TABLE appy_notification_log IS 'Log of all sent notifications for audit trail';
COMMENT ON TABLE appy_scheduled_notifications IS 'Queue of notifications to be sent';
COMMENT ON FUNCTION get_pending_appraisals_for_manager IS 'Returns list of employees with pending appraisals for a manager';
COMMENT ON FUNCTION schedule_monthly_reminders IS 'Schedules reminder notifications for the current period';
COMMENT ON FUNCTION process_scheduled_notifications IS 'Processes and sends due notifications';
