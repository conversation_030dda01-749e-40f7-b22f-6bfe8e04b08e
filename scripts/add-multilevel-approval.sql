-- Migration: Add Multi-Level Approval System
-- This script adds tables and functionality for cascading approval workflows

-- Create approval workflow table
CREATE TABLE IF NOT EXISTS appy_approval_workflows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  appraisal_id UUID REFERENCES appy_appraisals(id) ON DELETE CASCADE,
  workflow_type VARCHAR(50) DEFAULT 'standard' CHECK (workflow_type IN ('standard', 'manager_cascade', 'dual_manager')),
  current_level INTEGER DEFAULT 1,
  total_levels INTEGER DEFAULT 1,
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'rejected', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create approval steps table (defines the approval chain)
CREATE TABLE IF NOT EXISTS appy_approval_steps (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_id UUID REFERENCES appy_approval_workflows(id) ON DELETE CASCADE,
  step_level INTEGER NOT NULL,
  approver_id VARCHAR(255) NOT NULL, -- Clerk user ID
  approver_role VARCHAR(50), -- Role of the approver
  step_type VARCHAR(50) DEFAULT 'required' CHECK (step_type IN ('required', 'optional', 'conditional')),
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'skipped')),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejected_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  comments TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(workflow_id, step_level, approver_id)
);

-- Create approval history table (audit trail)
CREATE TABLE IF NOT EXISTS appy_approval_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_id UUID REFERENCES appy_approval_workflows(id) ON DELETE CASCADE,
  step_id UUID REFERENCES appy_approval_steps(id) ON DELETE CASCADE,
  action VARCHAR(50) NOT NULL CHECK (action IN ('approved', 'rejected', 'delegated', 'escalated', 'auto_approved')),
  actor_id VARCHAR(255) NOT NULL, -- Clerk user ID of person taking action
  actor_name VARCHAR(255),
  previous_status VARCHAR(50),
  new_status VARCHAR(50),
  comments TEXT,
  metadata JSONB, -- Additional context data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create approval delegation table (for when approvers delegate)
CREATE TABLE IF NOT EXISTS appy_approval_delegations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  original_approver_id VARCHAR(255) NOT NULL,
  delegate_approver_id VARCHAR(255) NOT NULL,
  workflow_id UUID REFERENCES appy_approval_workflows(id) ON DELETE CASCADE,
  delegation_reason TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by VARCHAR(255) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_approval_workflows_appraisal ON appy_approval_workflows(appraisal_id);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_status ON appy_approval_workflows(status);
CREATE INDEX IF NOT EXISTS idx_approval_steps_workflow ON appy_approval_steps(workflow_id);
CREATE INDEX IF NOT EXISTS idx_approval_steps_approver ON appy_approval_steps(approver_id);
CREATE INDEX IF NOT EXISTS idx_approval_steps_status ON appy_approval_steps(status);
CREATE INDEX IF NOT EXISTS idx_approval_history_workflow ON appy_approval_history(workflow_id);
CREATE INDEX IF NOT EXISTS idx_approval_history_actor ON appy_approval_history(actor_id);
CREATE INDEX IF NOT EXISTS idx_approval_delegations_original ON appy_approval_delegations(original_approver_id);
CREATE INDEX IF NOT EXISTS idx_approval_delegations_delegate ON appy_approval_delegations(delegate_approver_id);

-- Create function to update workflow updated_at timestamp
CREATE OR REPLACE FUNCTION update_approval_workflow_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for workflow updates
CREATE TRIGGER trigger_approval_workflow_updated_at
  BEFORE UPDATE ON appy_approval_workflows
  FOR EACH ROW
  EXECUTE FUNCTION update_approval_workflow_timestamp();

-- Create function to automatically progress workflow when step is approved
CREATE OR REPLACE FUNCTION progress_approval_workflow()
RETURNS TRIGGER AS $$
DECLARE
  workflow_record RECORD;
  next_pending_step RECORD;
  all_current_level_approved BOOLEAN;
BEGIN
  -- Only process when a step is approved
  IF NEW.status = 'approved' AND OLD.status != 'approved' THEN
    
    -- Get workflow details
    SELECT * INTO workflow_record
    FROM appy_approval_workflows
    WHERE id = NEW.workflow_id;
    
    -- Check if all steps at current level are approved
    SELECT NOT EXISTS (
      SELECT 1 FROM appy_approval_steps
      WHERE workflow_id = NEW.workflow_id
        AND step_level = workflow_record.current_level
        AND status IN ('pending', 'rejected')
        AND step_type = 'required'
    ) INTO all_current_level_approved;
    
    -- If all current level steps are approved, move to next level
    IF all_current_level_approved THEN
      -- Check if there are more levels
      IF workflow_record.current_level < workflow_record.total_levels THEN
        -- Move to next level
        UPDATE appy_approval_workflows
        SET current_level = current_level + 1,
            status = 'in_progress'
        WHERE id = NEW.workflow_id;
        
        -- Log the progression
        INSERT INTO appy_approval_history (
          workflow_id,
          step_id,
          action,
          actor_id,
          actor_name,
          previous_status,
          new_status,
          comments,
          metadata
        ) VALUES (
          NEW.workflow_id,
          NEW.id,
          'auto_approved',
          'system',
          'System',
          'level_' || workflow_record.current_level,
          'level_' || (workflow_record.current_level + 1),
          'Automatically progressed to next approval level',
          jsonb_build_object('previous_level', workflow_record.current_level, 'new_level', workflow_record.current_level + 1)
        );
        
      ELSE
        -- All levels completed, mark workflow as completed
        UPDATE appy_approval_workflows
        SET status = 'completed',
            completed_at = NOW()
        WHERE id = NEW.workflow_id;
        
        -- Update the original appraisal status
        UPDATE appy_appraisals
        SET status = 'approved',
            approved_at = NOW()
        WHERE id = workflow_record.appraisal_id;
        
        -- Log completion
        INSERT INTO appy_approval_history (
          workflow_id,
          step_id,
          action,
          actor_id,
          actor_name,
          previous_status,
          new_status,
          comments
        ) VALUES (
          NEW.workflow_id,
          NEW.id,
          'auto_approved',
          'system',
          'System',
          'in_progress',
          'completed',
          'Approval workflow completed successfully'
        );
      END IF;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic workflow progression
CREATE TRIGGER trigger_progress_approval_workflow
  AFTER UPDATE ON appy_approval_steps
  FOR EACH ROW
  EXECUTE FUNCTION progress_approval_workflow();

-- Create function to create approval workflow for an appraisal
CREATE OR REPLACE FUNCTION create_approval_workflow(
  p_appraisal_id UUID,
  p_employee_id UUID
)
RETURNS UUID AS $$
DECLARE
  workflow_id UUID;
  employee_record RECORD;
  manager_record RECORD;
  senior_manager_record RECORD;
  step_level INTEGER := 1;
  total_levels INTEGER := 1;
  workflow_type VARCHAR(50) := 'standard';
  manager_count INTEGER := 0;
BEGIN
  -- Get employee details and count managers
  SELECT e.*, COUNT(em.manager_id) as manager_count
  INTO employee_record
  FROM appy_employees e
  LEFT JOIN appy_employee_managers em ON e.id = em.employee_id
  WHERE e.id = p_employee_id
  GROUP BY e.id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Employee not found: %', p_employee_id;
  END IF;

  manager_count := employee_record.manager_count;

  -- Determine workflow type and levels based on manager configuration
  IF manager_count > 1 THEN
    workflow_type := 'dual_manager';
    total_levels := 2; -- All managers at level 1, then senior approval at level 2
  ELSE
    workflow_type := 'manager_cascade';
    total_levels := 2; -- Manager at level 1, senior manager at level 2
  END IF;

  -- Create the workflow
  INSERT INTO appy_approval_workflows (appraisal_id, workflow_type, total_levels)
  VALUES (p_appraisal_id, workflow_type, total_levels)
  RETURNING id INTO workflow_id;

  -- Handle dual manager approval (multiple managers must approve at level 1)
  IF workflow_type = 'dual_manager' THEN
    -- Add all managers as level 1 approvers
    INSERT INTO appy_approval_steps (
      workflow_id,
      step_level,
      approver_id,
      approver_role,
      step_type
    )
    SELECT
      workflow_id,
      1,
      em.manager_id,
      'manager',
      'required'
    FROM appy_employee_managers em
    WHERE em.employee_id = p_employee_id;

    -- Add senior manager or super-admin as level 2 approver
    SELECT m.manager_id INTO senior_manager_record
    FROM appy_employee_managers em
    JOIN appy_managers m ON em.manager_id = m.user_id
    WHERE em.employee_id = p_employee_id
      AND em.is_primary = true
    LIMIT 1;

    IF senior_manager_record IS NOT NULL THEN
      INSERT INTO appy_approval_steps (
        workflow_id,
        step_level,
        approver_id,
        approver_role,
        step_type
      ) VALUES (
        workflow_id,
        2,
        senior_manager_record,
        'senior_manager',
        'required'
      );
    ELSE
      -- Fallback to super-admin
      INSERT INTO appy_approval_steps (
        workflow_id,
        step_level,
        approver_id,
        approver_role,
        step_type
      ) VALUES (
        workflow_id,
        2,
        (SELECT user_id FROM appy_user_roles WHERE role = 'super-admin' LIMIT 1),
        'super-admin',
        'required'
      );
    END IF;

  ELSE
    -- Standard manager cascade workflow
    -- Level 1: Primary Manager Approval
    INSERT INTO appy_approval_steps (
      workflow_id,
      step_level,
      approver_id,
      approver_role,
      step_type
    )
    SELECT
      workflow_id,
      1,
      em.manager_id,
      'manager',
      'required'
    FROM appy_employee_managers em
    WHERE em.employee_id = p_employee_id
      AND em.is_primary = true
    LIMIT 1;

    -- Level 2: Senior Manager Approval
    SELECT m.manager_id INTO senior_manager_record
    FROM appy_employee_managers em
    JOIN appy_managers m ON em.manager_id = m.user_id
    WHERE em.employee_id = p_employee_id
      AND em.is_primary = true
    LIMIT 1;

    IF senior_manager_record IS NOT NULL THEN
      INSERT INTO appy_approval_steps (
        workflow_id,
        step_level,
        approver_id,
        approver_role,
        step_type
      ) VALUES (
        workflow_id,
        2,
        senior_manager_record,
        'senior_manager',
        'required'
      );
    ELSE
      -- Fallback to super-admin
      INSERT INTO appy_approval_steps (
        workflow_id,
        step_level,
        approver_id,
        approver_role,
        step_type
      ) VALUES (
        workflow_id,
        2,
        (SELECT user_id FROM appy_user_roles WHERE role = 'super-admin' LIMIT 1),
        'super-admin',
        'required'
      );
    END IF;
  END IF;

  -- Log workflow creation
  INSERT INTO appy_approval_history (
    workflow_id,
    action,
    actor_id,
    actor_name,
    new_status,
    comments,
    metadata
  ) VALUES (
    workflow_id,
    'auto_approved',
    'system',
    'System',
    'pending',
    'Approval workflow created for appraisal',
    jsonb_build_object(
      'workflow_type', workflow_type,
      'total_levels', total_levels,
      'manager_count', manager_count
    )
  );

  RETURN workflow_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to get pending approvals for a user
CREATE OR REPLACE FUNCTION get_pending_approvals_for_user(p_user_id VARCHAR(255))
RETURNS TABLE (
  workflow_id UUID,
  appraisal_id UUID,
  employee_name VARCHAR(255),
  department_name VARCHAR(255),
  step_level INTEGER,
  step_id UUID,
  submission_date TIMESTAMP WITH TIME ZONE,
  days_pending INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    aw.id as workflow_id,
    aw.appraisal_id,
    e.full_name as employee_name,
    d.name as department_name,
    ast.step_level,
    ast.id as step_id,
    a.submitted_at as submission_date,
    EXTRACT(DAY FROM (NOW() - a.submitted_at))::INTEGER as days_pending
  FROM appy_approval_workflows aw
  JOIN appy_approval_steps ast ON aw.id = ast.workflow_id
  JOIN appy_appraisals a ON aw.appraisal_id = a.id
  JOIN appy_employees e ON a.employee_id = e.id
  JOIN appy_departments d ON e.department_id = d.id
  WHERE ast.approver_id = p_user_id
    AND ast.status = 'pending'
    AND aw.status IN ('pending', 'in_progress')
    AND ast.step_level = aw.current_level
  ORDER BY a.submitted_at ASC;
END;
$$ LANGUAGE plpgsql;

-- Create function to approve/reject an approval step
CREATE OR REPLACE FUNCTION process_approval_step(
  p_step_id UUID,
  p_action VARCHAR(50), -- 'approved' or 'rejected'
  p_actor_id VARCHAR(255),
  p_actor_name VARCHAR(255),
  p_comments TEXT DEFAULT NULL,
  p_rejection_reason TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  step_record RECORD;
  workflow_record RECORD;
BEGIN
  -- Get step details
  SELECT * INTO step_record
  FROM appy_approval_steps
  WHERE id = p_step_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Approval step not found: %', p_step_id;
  END IF;
  
  -- Get workflow details
  SELECT * INTO workflow_record
  FROM appy_approval_workflows
  WHERE id = step_record.workflow_id;
  
  -- Update the step
  IF p_action = 'approved' THEN
    UPDATE appy_approval_steps
    SET status = 'approved',
        approved_at = NOW(),
        comments = p_comments
    WHERE id = p_step_id;
    
  ELSIF p_action = 'rejected' THEN
    UPDATE appy_approval_steps
    SET status = 'rejected',
        rejected_at = NOW(),
        rejection_reason = p_rejection_reason,
        comments = p_comments
    WHERE id = p_step_id;
    
    -- Mark workflow as rejected
    UPDATE appy_approval_workflows
    SET status = 'rejected'
    WHERE id = step_record.workflow_id;
    
    -- Mark appraisal as rejected
    UPDATE appy_appraisals
    SET status = 'rejected'
    WHERE id = workflow_record.appraisal_id;
  END IF;
  
  -- Log the action
  INSERT INTO appy_approval_history (
    workflow_id,
    step_id,
    action,
    actor_id,
    actor_name,
    previous_status,
    new_status,
    comments
  ) VALUES (
    step_record.workflow_id,
    p_step_id,
    p_action,
    p_actor_id,
    p_actor_name,
    step_record.status,
    p_action,
    COALESCE(p_comments, p_rejection_reason)
  );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

COMMENT ON TABLE appy_approval_workflows IS 'Multi-level approval workflows for appraisals';
COMMENT ON TABLE appy_approval_steps IS 'Individual approval steps within workflows';
COMMENT ON TABLE appy_approval_history IS 'Audit trail of all approval actions';
COMMENT ON FUNCTION create_approval_workflow IS 'Creates a multi-level approval workflow for an appraisal';
COMMENT ON FUNCTION get_pending_approvals_for_user IS 'Returns pending approval steps for a specific user';
COMMENT ON FUNCTION process_approval_step IS 'Processes approval or rejection of an approval step';
