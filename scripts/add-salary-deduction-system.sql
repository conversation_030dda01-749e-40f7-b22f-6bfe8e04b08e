-- Migration: Add Salary Deduction Comments System
-- This script adds accounting notes and salary adjustment tracking to appraisals

-- Add accounting-related columns to appraisals table
ALTER TABLE appy_appraisals 
ADD COLUMN IF NOT EXISTS accounting_notes TEXT,
ADD COLUMN IF NOT EXISTS salary_adjustment_amount DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS salary_adjustment_type VARCHAR(50) CHECK (salary_adjustment_type IN ('deduction', 'bonus', 'none')),
ADD COLUMN IF NOT EXISTS salary_adjustment_reason TEXT,
ADD COLUMN IF NOT EXISTS accounting_reviewed_by VARCHAR(255), -- Clerk user ID
ADD COLUMN IF NOT EXISTS accounting_reviewed_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS accounting_status VARCHAR(50) DEFAULT 'pending' CHECK (accounting_status IN ('pending', 'reviewed', 'processed', 'paid'));

-- Create salary adjustments history table
CREATE TABLE IF NOT EXISTS appy_salary_adjustments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  appraisal_id UUID REFERENCES appy_appraisals(id) ON DELETE CASCADE,
  employee_id UUID REFERENCES appy_employees(id) ON DELETE CASCADE,
  adjustment_type VARCHAR(50) NOT NULL CHECK (adjustment_type IN ('deduction', 'bonus', 'correction')),
  amount DECIMAL(10,2) NOT NULL,
  reason TEXT NOT NULL,
  description TEXT,
  
  -- Approval tracking
  requested_by VARCHAR(255) NOT NULL, -- Clerk user ID
  approved_by VARCHAR(255), -- Clerk user ID
  approval_status VARCHAR(50) DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  
  -- Processing tracking
  processed_by VARCHAR(255), -- Clerk user ID
  processed_at TIMESTAMP WITH TIME ZONE,
  processing_notes TEXT,
  
  -- Metadata
  period_id UUID REFERENCES appy_appraisal_periods(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create accounting comments table
CREATE TABLE IF NOT EXISTS appy_accounting_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  appraisal_id UUID REFERENCES appy_appraisals(id) ON DELETE CASCADE,
  commenter_id VARCHAR(255) NOT NULL, -- Clerk user ID
  comment_type VARCHAR(50) DEFAULT 'general' CHECK (comment_type IN ('general', 'deduction', 'bonus', 'correction', 'payment_issue')),
  comment TEXT NOT NULL,
  is_internal BOOLEAN DEFAULT true, -- Internal accounting notes vs notes visible to managers
  priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  
  -- Resolution tracking
  is_resolved BOOLEAN DEFAULT false,
  resolved_by VARCHAR(255), -- Clerk user ID
  resolved_at TIMESTAMP WITH TIME ZONE,
  resolution_notes TEXT,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment processing log table
CREATE TABLE IF NOT EXISTS appy_payment_processing_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  appraisal_id UUID REFERENCES appy_appraisals(id) ON DELETE CASCADE,
  employee_id UUID REFERENCES appy_employees(id) ON DELETE CASCADE,
  
  -- Payment details
  base_amount DECIMAL(10,2) NOT NULL,
  adjustment_amount DECIMAL(10,2) DEFAULT 0,
  final_amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(50) DEFAULT 'bank_transfer',
  
  -- Processing details
  processed_by VARCHAR(255) NOT NULL, -- Clerk user ID
  processing_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  payment_reference VARCHAR(255),
  
  -- Status tracking
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  status_notes TEXT,
  
  -- Metadata
  period_id UUID REFERENCES appy_appraisal_periods(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_appraisals_accounting_status ON appy_appraisals(accounting_status);
CREATE INDEX IF NOT EXISTS idx_appraisals_accounting_reviewed ON appy_appraisals(accounting_reviewed_by);
CREATE INDEX IF NOT EXISTS idx_salary_adjustments_appraisal ON appy_salary_adjustments(appraisal_id);
CREATE INDEX IF NOT EXISTS idx_salary_adjustments_employee ON appy_salary_adjustments(employee_id);
CREATE INDEX IF NOT EXISTS idx_salary_adjustments_status ON appy_salary_adjustments(approval_status);
CREATE INDEX IF NOT EXISTS idx_accounting_comments_appraisal ON appy_accounting_comments(appraisal_id);
CREATE INDEX IF NOT EXISTS idx_accounting_comments_commenter ON appy_accounting_comments(commenter_id);
CREATE INDEX IF NOT EXISTS idx_payment_log_appraisal ON appy_payment_processing_log(appraisal_id);
CREATE INDEX IF NOT EXISTS idx_payment_log_employee ON appy_payment_processing_log(employee_id);
CREATE INDEX IF NOT EXISTS idx_payment_log_status ON appy_payment_processing_log(status);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_accounting_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER trigger_salary_adjustments_updated_at
  BEFORE UPDATE ON appy_salary_adjustments
  FOR EACH ROW
  EXECUTE FUNCTION update_accounting_updated_at();

CREATE TRIGGER trigger_accounting_comments_updated_at
  BEFORE UPDATE ON appy_accounting_comments
  FOR EACH ROW
  EXECUTE FUNCTION update_accounting_updated_at();

CREATE TRIGGER trigger_payment_log_updated_at
  BEFORE UPDATE ON appy_payment_processing_log
  FOR EACH ROW
  EXECUTE FUNCTION update_accounting_updated_at();

-- Create function to add accounting notes to appraisal
CREATE OR REPLACE FUNCTION add_accounting_notes(
  p_appraisal_id UUID,
  p_notes TEXT,
  p_adjustment_amount DECIMAL(10,2) DEFAULT NULL,
  p_adjustment_type VARCHAR(50) DEFAULT NULL,
  p_adjustment_reason TEXT DEFAULT NULL,
  p_reviewed_by VARCHAR(255)
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE appy_appraisals
  SET 
    accounting_notes = p_notes,
    salary_adjustment_amount = COALESCE(p_adjustment_amount, salary_adjustment_amount),
    salary_adjustment_type = COALESCE(p_adjustment_type, salary_adjustment_type),
    salary_adjustment_reason = COALESCE(p_adjustment_reason, salary_adjustment_reason),
    accounting_reviewed_by = p_reviewed_by,
    accounting_reviewed_at = NOW(),
    accounting_status = 'reviewed'
  WHERE id = p_appraisal_id;
  
  -- Log the adjustment if provided
  IF p_adjustment_amount IS NOT NULL AND p_adjustment_type IS NOT NULL THEN
    INSERT INTO appy_salary_adjustments (
      appraisal_id,
      employee_id,
      adjustment_type,
      amount,
      reason,
      requested_by,
      approval_status
    )
    SELECT 
      p_appraisal_id,
      a.employee_id,
      p_adjustment_type,
      ABS(p_adjustment_amount),
      p_adjustment_reason,
      p_reviewed_by,
      'approved'
    FROM appy_appraisals a
    WHERE a.id = p_appraisal_id;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create function to get accounting summary for dashboard
CREATE OR REPLACE FUNCTION get_accounting_summary(
  p_period_id UUID DEFAULT NULL
)
RETURNS TABLE (
  total_appraisals INTEGER,
  pending_review INTEGER,
  reviewed_count INTEGER,
  processed_count INTEGER,
  total_adjustments DECIMAL(10,2),
  total_deductions DECIMAL(10,2),
  total_bonuses DECIMAL(10,2)
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_appraisals,
    COUNT(CASE WHEN a.accounting_status = 'pending' THEN 1 END)::INTEGER as pending_review,
    COUNT(CASE WHEN a.accounting_status = 'reviewed' THEN 1 END)::INTEGER as reviewed_count,
    COUNT(CASE WHEN a.accounting_status = 'processed' THEN 1 END)::INTEGER as processed_count,
    COALESCE(SUM(ABS(a.salary_adjustment_amount)), 0)::DECIMAL(10,2) as total_adjustments,
    COALESCE(SUM(CASE WHEN a.salary_adjustment_type = 'deduction' THEN ABS(a.salary_adjustment_amount) ELSE 0 END), 0)::DECIMAL(10,2) as total_deductions,
    COALESCE(SUM(CASE WHEN a.salary_adjustment_type = 'bonus' THEN ABS(a.salary_adjustment_amount) ELSE 0 END), 0)::DECIMAL(10,2) as total_bonuses
  FROM appy_appraisals a
  WHERE (p_period_id IS NULL OR a.period_id = p_period_id)
    AND a.status = 'approved';
END;
$$ LANGUAGE plpgsql;

-- Create function to get appraisals needing accounting review
CREATE OR REPLACE FUNCTION get_appraisals_for_accounting_review()
RETURNS TABLE (
  appraisal_id UUID,
  employee_name VARCHAR(255),
  department_name VARCHAR(255),
  manager_name VARCHAR(255),
  submitted_date TIMESTAMP WITH TIME ZONE,
  payment_status VARCHAR(50),
  current_adjustment_amount DECIMAL(10,2),
  current_adjustment_type VARCHAR(50),
  days_pending INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id as appraisal_id,
    e.full_name as employee_name,
    d.name as department_name,
    m.full_name as manager_name,
    a.submitted_at as submitted_date,
    a.payment_status,
    a.salary_adjustment_amount as current_adjustment_amount,
    a.salary_adjustment_type as current_adjustment_type,
    EXTRACT(DAY FROM (NOW() - a.submitted_at))::INTEGER as days_pending
  FROM appy_appraisals a
  JOIN appy_employees e ON a.employee_id = e.id
  JOIN appy_departments d ON e.department_id = d.id
  LEFT JOIN appy_managers m ON a.manager_id = m.user_id
  WHERE a.status = 'approved'
    AND a.accounting_status IN ('pending', 'reviewed')
  ORDER BY a.submitted_at ASC;
END;
$$ LANGUAGE plpgsql;

-- Create function to process payment
CREATE OR REPLACE FUNCTION process_payment(
  p_appraisal_id UUID,
  p_base_amount DECIMAL(10,2),
  p_processed_by VARCHAR(255),
  p_payment_reference VARCHAR(255) DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  payment_log_id UUID;
  appraisal_record RECORD;
  final_amount DECIMAL(10,2);
BEGIN
  -- Get appraisal details
  SELECT * INTO appraisal_record
  FROM appy_appraisals
  WHERE id = p_appraisal_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Appraisal not found: %', p_appraisal_id;
  END IF;
  
  -- Calculate final amount
  final_amount := p_base_amount + COALESCE(appraisal_record.salary_adjustment_amount, 0);
  
  -- Create payment log entry
  INSERT INTO appy_payment_processing_log (
    appraisal_id,
    employee_id,
    base_amount,
    adjustment_amount,
    final_amount,
    processed_by,
    payment_reference,
    period_id,
    status
  ) VALUES (
    p_appraisal_id,
    appraisal_record.employee_id,
    p_base_amount,
    COALESCE(appraisal_record.salary_adjustment_amount, 0),
    final_amount,
    p_processed_by,
    p_payment_reference,
    appraisal_record.period_id,
    'processing'
  )
  RETURNING id INTO payment_log_id;
  
  -- Update appraisal status
  UPDATE appy_appraisals
  SET accounting_status = 'processed'
  WHERE id = p_appraisal_id;
  
  RETURN payment_log_id;
END;
$$ LANGUAGE plpgsql;

COMMENT ON TABLE appy_salary_adjustments IS 'Salary adjustments (deductions/bonuses) for appraisals';
COMMENT ON TABLE appy_accounting_comments IS 'Accounting team comments and notes on appraisals';
COMMENT ON TABLE appy_payment_processing_log IS 'Log of payment processing activities';
COMMENT ON FUNCTION add_accounting_notes IS 'Adds accounting notes and salary adjustments to an appraisal';
COMMENT ON FUNCTION get_accounting_summary IS 'Returns accounting summary statistics';
COMMENT ON FUNCTION get_appraisals_for_accounting_review IS 'Returns appraisals that need accounting review';
COMMENT ON FUNCTION process_payment IS 'Processes payment for an appraisal and logs the transaction';
