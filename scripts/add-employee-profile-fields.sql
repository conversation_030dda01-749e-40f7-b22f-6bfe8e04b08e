-- Add employee profile fields migration
-- This script adds profile-related fields to the appy_employees table

-- Add new columns to appy_employees table
ALTER TABLE appy_employees
ADD COLUMN IF NOT EXISTS first_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS last_name <PERSON><PERSON><PERSON><PERSON>(255),
ADD COLUMN IF NOT EXISTS email VARCHAR(255),
ADD COLUMN IF NOT EXISTS bio TEXT,
ADD COLUMN IF NOT EXISTS linkedin_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS twitter_url VARCHAR(500),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Create index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_appy_employees_email ON appy_employees(email);

-- Backfill first_name and last_name from full_name
UPDATE appy_employees
SET 
  first_name = SPLIT_PART(full_name, ' ', 1),
  last_name = CASE 
    WHEN ARRAY_LENGTH(STRING_TO_ARRAY(full_name, ' '), 1) > 1 
    THEN SUBSTRING(full_name FROM POSITION(' ' IN full_name) + 1)
    ELSE ''
  END
WHERE first_name IS NULL OR last_name IS NULL;

-- Create appy_employee_kpis table
CREATE TABLE IF NOT EXISTS appy_employee_kpis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID NOT NULL REFERENCES appy_employees(id) ON DELETE CASCADE,
  kpi_name VARCHAR(255) NOT NULL,
  kpi_value VARCHAR(255),
  kpi_target VARCHAR(255),
  kpi_unit VARCHAR(100),
  period VARCHAR(100),
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appy_employee_kpis_employee_id ON appy_employee_kpis(employee_id);
CREATE INDEX IF NOT EXISTS idx_appy_employee_kpis_period ON appy_employee_kpis(period);

-- Add RLS (Row Level Security) policies for the new table
ALTER TABLE appy_employee_kpis ENABLE ROW LEVEL SECURITY;

-- Policy: Employees can view their own KPIs
CREATE POLICY "Employees can view own KPIs" ON appy_employee_kpis
  FOR SELECT
  USING (employee_id = auth.uid()::UUID);

-- Policy: Managers can view and manage their team's KPIs
CREATE POLICY "Managers can manage team KPIs" ON appy_employee_kpis
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM appy_employee_managers em
      WHERE em.employee_id = appy_employee_kpis.employee_id
      AND em.manager_id = auth.uid()
    )
  );

-- Policy: HR admins and accountants can view all KPIs
CREATE POLICY "HR and accounting can view all KPIs" ON appy_employee_kpis
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM appy_user_roles ur
      WHERE ur.user_id = auth.uid()
      AND ur.role IN ('hr-admin', 'accountant', 'super-admin')
    )
  );

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_appy_employees_updated_at
  BEFORE UPDATE ON appy_employees
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appy_employee_kpis_updated_at
  BEFORE UPDATE ON appy_employee_kpis
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add comment to document the purpose
COMMENT ON TABLE appy_employee_kpis IS 'Stores Key Performance Indicators (KPIs) for employees';
COMMENT ON COLUMN appy_employees.bio IS 'Employee biography or professional summary';
COMMENT ON COLUMN appy_employees.linkedin_url IS 'LinkedIn profile URL';
COMMENT ON COLUMN appy_employees.twitter_url IS 'X (Twitter) profile URL';