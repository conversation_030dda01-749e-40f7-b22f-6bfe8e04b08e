-- Migration: Add Appraisal Templates System
-- This script adds tables and functionality for appraisal templates

-- Create appraisal templates table
CREATE TABLE IF NOT EXISTS appy_appraisal_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VA<PERSON>HAR(255) NOT NULL,
  description TEXT,
  
  -- Template questions (flexible structure)
  questions JSONB NOT NULL DEFAULT '[]'::jsonb,
  
  -- Department/role assignment
  department_id UUID REFERENCES appy_departments(id) ON DELETE SET NULL,
  role_filter TEXT, -- JSON string for role-based filtering
  
  -- Template settings
  is_active BOOLEAN DEFAULT true,
  is_default BOOLEAN DEFAULT false,
  version INTEGER DEFAULT 1,
  
  -- Metadata
  created_by <PERSON><PERSON><PERSON><PERSON>(255), -- User ID who created the template
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create template usage tracking table
CREATE TABLE IF NOT EXISTS appy_template_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  template_id UUID REFERENCES appy_appraisal_templates(id) ON DELETE CASCADE,
  period_id UUID REFERENCES appy_appraisal_periods(id) ON DELETE CASCADE,
  manager_id VARCHAR(255) NOT NULL,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE(template_id, period_id, manager_id)
);

-- Add template_id to appraisals table (optional foreign key)
ALTER TABLE appy_appraisals 
ADD COLUMN IF NOT EXISTS template_id UUID REFERENCES appy_appraisal_templates(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_templates_active ON appy_appraisal_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_templates_department ON appy_appraisal_templates(department_id);
CREATE INDEX IF NOT EXISTS idx_templates_default ON appy_appraisal_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_appraisals_template ON appy_appraisals(template_id);
CREATE INDEX IF NOT EXISTS idx_template_usage_template ON appy_template_usage(template_id);
CREATE INDEX IF NOT EXISTS idx_template_usage_period ON appy_template_usage(period_id);

-- Insert default template with current hard-coded questions
INSERT INTO appy_appraisal_templates (name, description, questions, is_default, is_active, created_by)
VALUES (
  'Standard Monthly Review',
  'Default template for monthly employee performance reviews',
  '[
    {
      "id": "q1",
      "type": "radio",
      "question": "Overall performance this month:",
      "required": true,
      "options": [
        {"value": "below-expectations", "label": "Below Expectations"},
        {"value": "meets-expectations", "label": "Meets Expectations"},
        {"value": "exceeds-expectations", "label": "Exceeds Expectations"}
      ]
    },
    {
      "id": "q2", 
      "type": "checkbox",
      "question": "Completed all assigned tasks?",
      "required": false
    },
    {
      "id": "q3",
      "type": "text",
      "question": "Primary project/focus area:",
      "required": true,
      "placeholder": "e.g., Project Phoenix, Q3 Marketing Campaign"
    },
    {
      "id": "q4",
      "type": "textarea", 
      "question": "Achievements and areas for improvement:",
      "required": true,
      "placeholder": "Summarize key accomplishments and any challenges faced...",
      "rows": 5
    },
    {
      "id": "q5",
      "type": "text",
      "question": "Next month''s goals and focus:",
      "required": true,
      "placeholder": "e.g., Lead design for new feature, improve API documentation"
    }
  ]'::jsonb,
  true,
  true,
  'system'
) ON CONFLICT DO NOTHING;

-- Update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_templates_updated_at 
    BEFORE UPDATE ON appy_appraisal_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE appy_appraisal_templates IS 'Stores reusable appraisal form templates';
COMMENT ON COLUMN appy_appraisal_templates.questions IS 'JSON array of question objects with type, text, options, validation rules';
COMMENT ON COLUMN appy_appraisal_templates.department_id IS 'Optional department assignment for template';
COMMENT ON COLUMN appy_appraisal_templates.role_filter IS 'JSON string for role-based template filtering';
COMMENT ON COLUMN appy_appraisal_templates.is_default IS 'Whether this is the default template for new appraisals';
COMMENT ON TABLE appy_template_usage IS 'Tracks template usage statistics for analytics';