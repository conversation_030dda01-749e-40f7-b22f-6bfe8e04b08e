-- Migration: Add Employee Feedback System
-- This script adds tables and functionality for employee feedback and complaints

-- Create employee feedback table
CREATE TABLE IF NOT EXISTS appy_employee_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  submitter_id UUID REFERENCES appy_employees(id) ON DELETE CASCADE,
  target_employee_id UUID REFERENCES appy_employees(id) ON DELETE CASCADE,
  feedback_type VARCHAR(50) NOT NULL CHECK (feedback_type IN ('complaint', 'suggestion', 'recognition', 'concern', 'initiative', 'general')),
  category VARCHAR(50) CHECK (category IN ('performance', 'behavior', 'communication', 'teamwork', 'leadership', 'process', 'other')),
  subject VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  is_anonymous BOOLEAN DEFAULT false,
  priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  
  -- Status tracking
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'investigating', 'resolved', 'closed', 'escalated')),
  
  -- HR response
  hr_response TEXT,
  hr_notes TEXT, -- Internal notes not visible to submitter
  reviewed_by UUID REFERENCES appy_managers(user_id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  
  -- Resolution tracking
  resolution_summary TEXT,
  resolved_by UUID REFERENCES appy_managers(user_id),
  resolved_at TIMESTAMP WITH TIME ZONE,
  
  -- Follow-up
  requires_followup BOOLEAN DEFAULT false,
  followup_date DATE,
  followup_notes TEXT,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT feedback_not_self CHECK (submitter_id != target_employee_id OR target_employee_id IS NULL)
);

-- Create feedback attachments table (for future file uploads)
CREATE TABLE IF NOT EXISTS appy_feedback_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  feedback_id UUID REFERENCES appy_employee_feedback(id) ON DELETE CASCADE,
  filename VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size INTEGER,
  mime_type VARCHAR(100),
  uploaded_by UUID REFERENCES appy_employees(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create feedback status history table
CREATE TABLE IF NOT EXISTS appy_feedback_status_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  feedback_id UUID REFERENCES appy_employee_feedback(id) ON DELETE CASCADE,
  previous_status VARCHAR(50),
  new_status VARCHAR(50) NOT NULL,
  changed_by UUID REFERENCES appy_managers(user_id),
  change_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create feedback comments table (for HR internal discussions)
CREATE TABLE IF NOT EXISTS appy_feedback_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  feedback_id UUID REFERENCES appy_employee_feedback(id) ON DELETE CASCADE,
  commenter_id UUID REFERENCES appy_managers(user_id),
  comment TEXT NOT NULL,
  is_internal BOOLEAN DEFAULT true, -- Internal HR comments vs responses to submitter
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_feedback_submitter ON appy_employee_feedback(submitter_id);
CREATE INDEX IF NOT EXISTS idx_feedback_target ON appy_employee_feedback(target_employee_id);
CREATE INDEX IF NOT EXISTS idx_feedback_type ON appy_employee_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_feedback_status ON appy_employee_feedback(status);
CREATE INDEX IF NOT EXISTS idx_feedback_created ON appy_employee_feedback(created_at);
CREATE INDEX IF NOT EXISTS idx_feedback_priority ON appy_employee_feedback(priority);
CREATE INDEX IF NOT EXISTS idx_feedback_reviewed_by ON appy_employee_feedback(reviewed_by);

CREATE INDEX IF NOT EXISTS idx_feedback_attachments_feedback ON appy_feedback_attachments(feedback_id);
CREATE INDEX IF NOT EXISTS idx_feedback_history_feedback ON appy_feedback_status_history(feedback_id);
CREATE INDEX IF NOT EXISTS idx_feedback_comments_feedback ON appy_feedback_comments(feedback_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_feedback_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER trigger_feedback_updated_at
  BEFORE UPDATE ON appy_employee_feedback
  FOR EACH ROW
  EXECUTE FUNCTION update_feedback_updated_at();

CREATE TRIGGER trigger_feedback_comments_updated_at
  BEFORE UPDATE ON appy_feedback_comments
  FOR EACH ROW
  EXECUTE FUNCTION update_feedback_updated_at();

-- Create function to log status changes
CREATE OR REPLACE FUNCTION log_feedback_status_change()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO appy_feedback_status_history (
      feedback_id,
      previous_status,
      new_status,
      changed_by,
      change_reason
    ) VALUES (
      NEW.id,
      OLD.status,
      NEW.status,
      NEW.reviewed_by,
      CASE 
        WHEN NEW.status = 'under_review' THEN 'Feedback assigned for review'
        WHEN NEW.status = 'investigating' THEN 'Investigation started'
        WHEN NEW.status = 'resolved' THEN 'Issue resolved'
        WHEN NEW.status = 'closed' THEN 'Feedback closed'
        WHEN NEW.status = 'escalated' THEN 'Escalated to higher management'
        ELSE 'Status updated'
      END
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for status change logging
CREATE TRIGGER trigger_log_feedback_status_change
  AFTER UPDATE ON appy_employee_feedback
  FOR EACH ROW
  EXECUTE FUNCTION log_feedback_status_change();

-- Create function to get feedback statistics for HR dashboard
CREATE OR REPLACE FUNCTION get_feedback_statistics(
  start_date DATE DEFAULT NULL,
  end_date DATE DEFAULT NULL
)
RETURNS TABLE (
  total_feedback INTEGER,
  pending_count INTEGER,
  under_review_count INTEGER,
  resolved_count INTEGER,
  by_type JSONB,
  by_priority JSONB,
  avg_resolution_days NUMERIC
) AS $$
BEGIN
  -- Set default date range if not provided
  IF start_date IS NULL THEN
    start_date := CURRENT_DATE - INTERVAL '30 days';
  END IF;
  
  IF end_date IS NULL THEN
    end_date := CURRENT_DATE;
  END IF;

  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_feedback,
    COUNT(CASE WHEN f.status = 'pending' THEN 1 END)::INTEGER as pending_count,
    COUNT(CASE WHEN f.status = 'under_review' THEN 1 END)::INTEGER as under_review_count,
    COUNT(CASE WHEN f.status = 'resolved' THEN 1 END)::INTEGER as resolved_count,
    
    -- Feedback by type
    jsonb_object_agg(
      f.feedback_type, 
      COUNT(CASE WHEN f.feedback_type IS NOT NULL THEN 1 END)
    ) FILTER (WHERE f.feedback_type IS NOT NULL) as by_type,
    
    -- Feedback by priority
    jsonb_object_agg(
      f.priority, 
      COUNT(CASE WHEN f.priority IS NOT NULL THEN 1 END)
    ) FILTER (WHERE f.priority IS NOT NULL) as by_priority,
    
    -- Average resolution time
    AVG(
      CASE 
        WHEN f.resolved_at IS NOT NULL 
        THEN EXTRACT(EPOCH FROM (f.resolved_at - f.created_at)) / 86400 
      END
    )::NUMERIC(10,2) as avg_resolution_days
    
  FROM appy_employee_feedback f
  WHERE f.created_at::DATE BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql;

-- Create function to get pending feedback for HR
CREATE OR REPLACE FUNCTION get_pending_feedback_for_hr()
RETURNS TABLE (
  feedback_id UUID,
  submitter_name VARCHAR(255),
  target_name VARCHAR(255),
  feedback_type VARCHAR(50),
  subject VARCHAR(255),
  priority VARCHAR(20),
  created_at TIMESTAMP WITH TIME ZONE,
  days_pending INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    f.id as feedback_id,
    CASE 
      WHEN f.is_anonymous THEN 'Anonymous'
      ELSE s.full_name
    END as submitter_name,
    COALESCE(t.full_name, 'General Feedback') as target_name,
    f.feedback_type,
    f.subject,
    f.priority,
    f.created_at,
    EXTRACT(DAY FROM (NOW() - f.created_at))::INTEGER as days_pending
  FROM appy_employee_feedback f
  LEFT JOIN appy_employees s ON f.submitter_id = s.id
  LEFT JOIN appy_employees t ON f.target_employee_id = t.id
  WHERE f.status IN ('pending', 'under_review')
  ORDER BY 
    CASE f.priority 
      WHEN 'urgent' THEN 1
      WHEN 'high' THEN 2
      WHEN 'medium' THEN 3
      WHEN 'low' THEN 4
    END,
    f.created_at ASC;
END;
$$ LANGUAGE plpgsql;

-- Insert sample feedback types and categories for reference
COMMENT ON TABLE appy_employee_feedback IS 'Employee feedback and complaints system';
COMMENT ON COLUMN appy_employee_feedback.feedback_type IS 'Type: complaint, suggestion, recognition, concern, initiative, general';
COMMENT ON COLUMN appy_employee_feedback.category IS 'Category: performance, behavior, communication, teamwork, leadership, process, other';
COMMENT ON COLUMN appy_employee_feedback.priority IS 'Priority: low, medium, high, urgent';
COMMENT ON COLUMN appy_employee_feedback.status IS 'Status: pending, under_review, investigating, resolved, closed, escalated';

-- Create RLS policies (Row Level Security)
ALTER TABLE appy_employee_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_feedback_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_feedback_status_history ENABLE ROW LEVEL SECURITY;

-- Policy: Employees can view their own submitted feedback
CREATE POLICY feedback_submitter_access ON appy_employee_feedback
  FOR SELECT
  USING (submitter_id = (SELECT id FROM appy_employees WHERE id = auth.uid()::uuid));

-- Policy: HR and managers can view all feedback
CREATE POLICY feedback_hr_access ON appy_employee_feedback
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM appy_user_roles ur 
      WHERE ur.user_id = auth.jwt() ->> 'sub' 
      AND ur.role IN ('hr-admin', 'super-admin')
    )
  );

-- Policy: Employees can insert their own feedback
CREATE POLICY feedback_insert_own ON appy_employee_feedback
  FOR INSERT
  WITH CHECK (submitter_id = (SELECT id FROM appy_employees WHERE id = auth.uid()::uuid));

COMMENT ON FUNCTION get_feedback_statistics IS 'Returns feedback statistics for HR dashboard';
COMMENT ON FUNCTION get_pending_feedback_for_hr IS 'Returns pending feedback items for HR review';
